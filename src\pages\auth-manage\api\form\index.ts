import type { AnnexFile, AnnexFileParams, FileOptions } from './types'

export * from './types'

export const baseURL = remoteConfig.hosts.gateway + '/form'

const http = createService(baseURL)

enum API {
  UPLOAD_FILES = '/api/FormExtend/UploadFiles',
  UPLOAD_FILE = '/api/FormExtend/UploadFile',
}

export function uploadFiles(file: FormData) {
  return unFetchData<FileOptions>(http(API.UPLOAD_FILES, streamFetchOptions).post(file).json())
}

export function uploadAnnexFiles(body: AnnexFileParams) {
  return unFetchData<AnnexFile[]>(http(API.UPLOAD_FILE, streamFetchOptions).post(body).json())
}
