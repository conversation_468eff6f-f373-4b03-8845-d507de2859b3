<script setup lang="ts">
import Dialog from './components/Dialog'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import VConsole from 'vconsole'

const route = useRoute()
watch(
  route,
  () => {
    const debug = new URL(location.href).searchParams.get('debug')
    debug && new VConsole()
  },
  { immediate: true },
)
</script>

<template>
  <el-config-provider :locale="zhCn" size="default">
    <RouterView v-slot="{ Component }">
      <template v-if="Component">
        <Transition mode="out-in">
          <KeepAlive>
            <Suspense>
              <transition name="fade">
                <!-- 主要内容 -->
                <component :is="Component"></component>
              </transition>

              <!-- 加载中状态 -->
              <template #fallback> 正在加载... </template>
            </Suspense>
          </KeepAlive>
        </Transition>
      </template>
    </RouterView>
  </el-config-provider>
  <Dialog />
</template>
