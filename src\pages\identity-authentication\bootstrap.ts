import 'uno.css'
import './style.css'
import '@duo-common/icon-v3/dist/style.css'

import App from './App.vue'
import router from './router'
import { setupDuoIcon } from '@duo-common/icon-v3'
import { initMenus, initOcSelect } from '@unpages/init'
import { useUser } from '~/oidc'
import { createApp } from 'vue'
import { AuthProvider, type AuthProviderProps } from 'vue-oidc-provider'

const oidcSettings = {
  ...remoteConfig.oidcSettings,
  redirect_uri: window.location.href,
  onSigninCallback: () => {
    location.href = window.location.pathname
  },
} satisfies AuthProviderProps

const user = useUser()
const { app } = useApi()

initOcSelect(remoteConfig.hosts.gateway)
const { data } = await app.getRoles({
  module: 'identity-authentication',
  roles: user?.profile.RoleIds!,
  children: true,
  isTop: false,
})
if (data.value?.code === 1) {
  initMenus(data.value.data)
}
setupDuoIcon({
  aliId: remoteConfig.aliId,
  textTip: false,
})

createApp(
  h(
    AuthProvider,
    { options: oidcSettings },
    {
      default: () => h(App),
    },
  ),
)
  .use(router)
  .mount('#app')
