<script lang="ts" setup>
import type { UserBasic } from '../utils'
import type { FormInstance, FormRules } from 'element-plus'

const { ids4 } = useApi()
const formRef = ref<FormInstance>()
const form = reactive<UserBasic>({
  userName: '',
  nickName: '',
})
const rules = reactive<FormRules<UserBasic>>({
  userName: [{ required: true, message: '账号/工号不能为空', trigger: 'blur' }],
  nickName: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
})

function validate(): Promise<UserBasic> {
  return new Promise((resolve, reject) => {
    formRef.value?.validate(async (valid) => {
      if (valid) {
        const { data } = await ids4.validateUsername(form)
        if (data.value?.code === 1) {
          if (data.value?.data === true) {
            resolve(form)
          } else {
            reject('姓名和账号/工号不匹配')
          }
        } else {
          reject(data.value?.message)
        }
      } else {
        reject('表单验证不通过')
      }
    })
  })
}

function clearForm() {
  form.userName = ''
  form.nickName = ''
  formRef.value?.clearValidate()
}

defineExpose({
  validate,
  clearForm,
})
</script>

<template>
  <el-form :model="form" :rules="rules" ref="formRef" label-position="top" label-width="auto">
    <el-form-item label="姓名" prop="nickName">
      <el-input v-model="form.nickName" placeholder="请输入姓名" />
    </el-form-item>
    <el-form-item label="账号" prop="userName">
      <el-input v-model="form.userName" placeholder="请输入账号/工号" />
    </el-form-item>
  </el-form>
</template>

<style lang="less" scoped></style>
