<script lang="ts" setup>
import type { TransformClient } from '../logic'
import { AuthProtocol } from '~identity-authentication/api/ids4'
import type { FormInstance, FormRules } from 'element-plus'

const props = defineProps<{
  modelValue: TransformClient
}>()
const emit = defineEmits(['update:modelValue'])
const form = useVModel(props, 'modelValue', emit)
const iconType = ref<0 | 1>(0)
const checkAll = ref(false)
const isIndeterminate = ref(false)

//初始化iconType
const stop = watchEffect(() => {
  if (props.modelValue.icon) {
    iconType.value = props.modelValue.icon.startsWith('http') ? 1 : 0
    stop()
  }
})

const urlPattern = /^(https?:\/\/)?([a-zA-Z0-9_-]+\.)+[a-zA-Z]{2,}(\/[a-zA-Z0-9_-]+)*\/?$/

const rules = reactive<FormRules<TransformClient>>({
  icon: { required: false, message: '此字段必填', trigger: 'prompt' },
  clientName: { required: true, message: '此字段必填', trigger: 'blur' },
  ownerIp: [
    {
      required: true,
      message: '此字段必填',
      trigger: 'blur',
    },
    {
      type: 'string',
      pattern:
        /^(([01]?[0-9]{1,2}|2[0-4][0-9]|25[0-5])\.){3}([01]?[0-9]{1,2}|2[0-4][0-9]|25[0-5])$/,
      message: '输入正确的IP地址',
      trigger: 'blur',
    },
  ],
  ownerPort: [
    { required: true, message: '此字段必填', trigger: 'blur' },
    {
      type: 'string',
      pattern: /^[1-9][0-9]{0,3}$|^0$/,
      message: '输入正确的端口',
      trigger: 'blur',
    },
  ],
  enabled: { required: true, message: '此字段必填', trigger: 'blur' },
  allowedRoles: [
    { required: true, message: '此字段必填', trigger: 'blur' },
    {
      type: 'array',
      validator: (rule, value, callback) => {
        if (!value.length) {
          callback(new Error('至少选择一个模块'))
        } else {
          callback()
        }
      },
    },
  ],
  returnUserProperties: [
    {
      required: true,
      message: '此字段必填',
      trigger: 'change',
    },
  ],
  ownerName: { required: true, message: '此字段必填', trigger: 'blur' },
  serviceName: { required: false, message: '此字段必填', trigger: 'blur' },
  redirectUris: {
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (!redirectUri.value || urlPattern.test(redirectUri.value)) {
        callback()
      } else {
        callback(new Error('输入合法的URL地址'))
      }
    },
  },
  postLogoutRedirectUris: {
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (!postLogoutRedirectUri.value || urlPattern.test(postLogoutRedirectUri.value)) {
        callback()
      } else {
        callback(new Error('输入合法的URL地址'))
      }
    },
  },
})
const redirectUri = ref('')
const postLogoutRedirectUri = ref('')
const formInstance = ref<FormInstance>()
const { copy, copied } = useClipboard()
const { copy: copy1, copied: copied1 } = useClipboard()
const { ids4 } = useApi()

defineExpose({ validate, clearValidate })

const { data: userProperties } = await ids4.getUserProperties()
watch([form, userProperties], () => {
  checkAll.value = form.value.returnUserProperties.length >= userProperties.value!.data?.length
})
function handleCheckAllChange(val: boolean) {
  form.value.returnUserProperties = val
    ? userProperties.value?.data.map(({ code }) => code) || []
    : []
  isIndeterminate.value = false
}
function handleCheckedUserPropertiesChange(value: string[]) {
  const checkedCount = value.length
  checkAll.value = checkedCount === userProperties.value?.data.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < userProperties.value!.data.length
}
async function handleOwnerName() {
  const { data } = await useOcSelector('D', {
    multiple: false,
  })
  if (!data.value?.length) return
  form.value.ownerName = data.value[0].name
  formInstance.value?.validateField('ownerName')
}
async function handleOwnerContactPerson() {
  const { data } = await useOcSelector('U', {
    multiple: false,
  })
  if (!data.value?.length) return
  form.value.ownerContactPerson = data.value[0].name
}
async function handleAllowedRoles() {
  const { data } = await useOcSelector('ORG', {
    multiple: true,
    selected: form.value.allowedRoles,
  })
  if (!data.value?.length) return
  form.value.allowedRoles = data.value.map((item) => ({
    nodeId: item.id,
    tab: item.type,
    name: item.name,
  }))
  formInstance.value?.validateField('allowedRoles')
}
function handleRedirectUri() {
  if (!redirectUri.value) return
  formInstance.value?.validateField('redirectUris', (isValid) => {
    if (isValid) {
      form.value.redirectUris.push(redirectUri.value)
      redirectUri.value = ''
    }
  })
}
function handlePostLogoutRedirectUri() {
  if (!postLogoutRedirectUri.value) return
  formInstance.value?.validateField('postLogoutRedirectUris', (isValid) => {
    if (isValid) {
      form.value.postLogoutRedirectUris.push(postLogoutRedirectUri.value)
      postLogoutRedirectUri.value = ''
    }
  })
}
async function validate(): Promise<void> {
  return new Promise((resolve, reject) => {
    formInstance.value?.validate((valid, fields) => {
      if (valid) {
        resolve()
      } else {
        reject(fields)
      }
    })
  })
}
function clearValidate() {
  formInstance.value?.clearValidate()
}
</script>

<template>
  <el-form
    ref="formInstance"
    :model="form"
    label-width="110px"
    label-position="left"
    :rules="rules"
  >
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="认证类型">
          <el-radio-group v-model="form.authProtocol">
            <el-radio :value="AuthProtocol.OAuth"> OAuth </el-radio>
            <el-radio :value="AuthProtocol.Cas"> Cas </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <template v-if="form.authProtocol === AuthProtocol.OAuth">
        <el-col :span="12">
          <el-form-item label="客户端ID">
            <div class="flex items-center w-full">
              <el-input
                :model-value="form.clientId"
                disabled
                placeholder="创建完成自动生成"
                class="flex-1"
              />
              <el-tooltip effect="dark" content="复制" placement="bottom" :show-after="1000">
                <div
                  i-mingcute-copy-2-fill
                  w-30px
                  cursor-pointer
                  @click="() => copy(form.clientId)"
                  v-if="!copied"
                />
                <div i-mingcute-copy-3-line w-30px cursor-pointer color-green-600 v-else />
              </el-tooltip>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户端秘钥">
            <div class="flex items-center w-full">
              <el-input :model-value="form.clientSecret" disabled placeholder="创建完成自动生成" />
              <el-tooltip effect="dark" content="复制" placement="bottom" :show-after="1000">
                <div
                  i-mingcute-copy-2-fill
                  w-30px
                  cursor-pointer
                  @click="() => copy1(form.clientSecret)"
                  v-if="!copied1"
                />
                <div i-mingcute-copy-3-line w-30px cursor-pointer color-green-600 v-else />
              </el-tooltip>
            </div>
          </el-form-item>
        </el-col>
      </template>
      <el-col :span="12">
        <el-form-item label="应用图标" prop="icon">
          <div class="flex items-start justify-between w-full">
            <div>
              <IconPicker
                v-if="iconType === 0"
                v-model="form.icon"
                @update:model-value="() => formInstance?.validateField('icon')"
              />
              <Upload
                :limit="1"
                :files="form.icon ? [form.icon] : []"
                @update:model-value="
                  (evt) => {
                    form.icon = useImageURL(evt.map((item) => item.fileAccessPath)[0])
                  }
                "
                v-else-if="iconType === 1"
              />
            </div>

            <el-radio-group v-model="iconType" @update:model-value="() => (form.icon = '')">
              <el-radio :value="0" size="large" label="图标" />
              <el-radio :value="1" size="large" label="图片" />
            </el-radio-group>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="应用名称" prop="clientName">
          <el-input v-model="form.clientName" placeholder="输入应用名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="服务器IP" prop="ownerIp">
          <el-input v-model="form.ownerIp" placeholder="输入服务器IP" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="应用访问端口" prop="ownerPort">
          <el-input v-model="form.ownerPort" placeholder="输入应用访问端口" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="访问权限" prop="allowedRoles">
          <template v-if="form.allowedRoles?.length">
            <el-tag
              v-for="(item, index) in form.allowedRoles"
              :key="item.nodeId"
              effect="dark"
              size="large"
              class="mr-6px"
              closable
              @close="() => form.allowedRoles?.splice(index, 1)"
            >
              {{ item.name }}
            </el-tag>
          </template>
          <el-button type="primary" @click="handleAllowedRoles">设置</el-button>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="返回属性" prop="returnUserProperties">
          <el-checkbox
            v-model="checkAll"
            :indeterminate="isIndeterminate"
            @change="(e) => handleCheckAllChange(e as boolean)"
            class="mr-20px!"
            >全选
          </el-checkbox>
          <el-checkbox-group
            v-model="form.returnUserProperties"
            @change="(e) => handleCheckedUserPropertiesChange(e as string[])"
          >
            <el-checkbox
              v-for="item in userProperties?.data"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="是否启用" prop="enable">
          <el-switch v-model="form.enabled" size="large" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="登录回调地址" prop="redirectUris">
          <div class="flex w-full mb-10px">
            <el-input
              v-model="redirectUri"
              placeholder="输入合法的URL地址"
              class="w-100% mr-10px"
            />
            <el-button type="primary" @click="handleRedirectUri">添加</el-button>
          </div>
          <template v-if="form.redirectUris.length">
            <el-tag
              v-for="(item, i) in form.redirectUris"
              :key="i"
              size="large"
              closable
              @close="() => form.redirectUris.splice(i, 1)"
              class="mr-10px my-2px"
            >
              {{ item }}
            </el-tag>
          </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="注销地址" prop="postLogoutRedirectUris">
          <div class="flex w-full mb-10px">
            <el-input
              v-model="postLogoutRedirectUri"
              placeholder="输入合法的URL地址"
              class="w-100% mr-10px"
            />
            <el-button type="primary" @click="handlePostLogoutRedirectUri">添加</el-button>
          </div>
          <template v-if="form.postLogoutRedirectUris.length">
            <el-tag
              v-for="(item, i) in form.postLogoutRedirectUris"
              :key="i"
              size="large"
              closable
              @close="() => form.postLogoutRedirectUris.splice(i, 1)"
              class="mr-10px my-2px"
            >
              {{ item }}
            </el-tag>
          </template>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="text-1.1rem font-bold color-gray-6 my-10px">所属单位信息</div>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="单位名称" prop="ownerName">
          <template v-if="form.ownerName">
            <el-tag size="large" class="mr-6px" closable @close="() => (form.ownerName = '')">
              {{ form.ownerName }}
            </el-tag>
          </template>
          <el-button type="primary" @click="handleOwnerName" plain>设置</el-button>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="应用管理员">
          <template v-if="form.ownerContactPerson">
            <el-tag
              size="large"
              class="mr-6px"
              closable
              @close="() => (form.ownerContactPerson = '')"
            >
              {{ form.ownerContactPerson }}
            </el-tag>
          </template>
          <el-button type="primary" @click="handleOwnerContactPerson" plain>设置</el-button>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="管理员手机号">
          <el-input v-model="form.ownerContactPhone" />
        </el-form-item>
      </el-col>
    </el-row>

    <div class="text-1.1rem font-bold color-gray-6 my-10px">服务商信息</div>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="服务单位" prop="serviceName">
          <el-input v-model="form.serviceName" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="服务人员">
          <el-input v-model="form.serviceContactPerson" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="服务人员手机号">
          <el-input v-model="form.serviceContactPhone" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="less" scoped></style>
