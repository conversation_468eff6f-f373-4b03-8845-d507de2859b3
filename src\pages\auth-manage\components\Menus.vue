<script lang="ts" setup>
import { menuConfig } from '@unpages/init'

const route = useRoute()
const favicon = new URL('/favicon.ico', import.meta.url).href
const title = remoteConfig.title

onMounted(() => {
  console.log(menuConfig.value.menus)
})
</script>

<template>
  <el-menu
    :default-active="route.path"
    mode="horizontal"
    :ellipsis="false"
    style="
      --el-menu-base-level-padding: 24px;
      --el-menu-bg-color: var(--el-color-primary);
      --el-menu-text-color: var(--el-color-white);
      --el-menu-active-color: white;
      --el-menu-hover-bg-color: var(--active-bg-color);
      --el-menu-hover-text-color: white;
    "
  >
    <div class="flex items-center ml-30px mr-130px" @click="$router.push('/')">
      <el-image :src="favicon" class="h-34px" />
      <div class="font-bold text-1.1rem tracking-widest ml-10px">{{ title }}</div>
    </div>

    <template v-for="item in menuConfig.menus" :key="item.id">
      <MenuItem :route="item" />
    </template>

    <div class="flex-grow" />
  </el-menu>
</template>

<style scoped>
.theme--default .el-menu {
  --active-bg-color: #008bfb;
  --el-color-primary-dark-2: #008bfb;
}
.el-menu {
  border: 0 !important;
  display: flex;
  align-items: center;
}
.el-menu-item {
  height: 40px;
  border-radius: 5px;
  border-bottom: 0 !important;
  margin: 4px;
}
</style>
