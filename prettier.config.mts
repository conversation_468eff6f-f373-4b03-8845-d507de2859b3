import { PluginConfig } from '@ianvs/prettier-plugin-sort-imports'
import { Config } from 'prettier'

export default {
  semi: false,
  tabWidth: 2,
  singleQuote: true,
  printWidth: 100,
  arrowParens: 'always',
  jsxSingleQuote: false,
  trailingComma: 'all',
  plugins: ['@ianvs/prettier-plugin-sort-imports'],
  importOrderParserPlugins: ['typescript', 'jsx', 'decorators-legacy'],
  importOrderTypeScriptVersion: '5.0.0',
  importOrder: ['<THIRD_PARTY_MODULES>', ''],
} satisfies Config | PluginConfig
