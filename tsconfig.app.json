{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "packages/**/*", "src/**/*", "src/**/*.ts", "src/**/*.vue", "components.d.ts", "auto-imports.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"noImplicitAny": false, "composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "~/*": ["./src/*"], "~auth-manage/*": ["./src/pages/auth-manage/*"], "~performance/*": ["./src/pages/performance/*"], "~identity-authentication/*": ["./src/pages/identity-authentication/*"], "~authentication-client/*": ["./src/pages/authentication-client/*"]}}}