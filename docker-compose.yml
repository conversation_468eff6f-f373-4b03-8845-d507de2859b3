version: '3.4'

services:
  iduo-studio-modules:
    image: iduo-studio-modules
    container_name: iduo-studio-modules
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - 6772:6772
    restart: always
    network_mode: bridge
    volumes:
      - ./dist:/usr/share/nginx/html/
      # http
      - ./nginx.conf:/etc/nginx/nginx.conf
      # or
      # https SSL
      # - ./nginx.ssl.conf:/etc/nginx/nginx.conf
      # - ./ssl/:/etc/nginx/ssl/
