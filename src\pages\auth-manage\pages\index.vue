<script setup lang="ts">
import { navigateTo } from '@unpages/utils'
import type { Client, ClientsParams } from '~auth-manage/api/ids4'
import { findScope } from '~auth-manage/utils'
import PhMagnifyingGlassLight from '~icons/ph/magnifying-glass-light'
import dayjs from 'dayjs'
import type { TableInstance } from 'element-plus'

useTitle('授权管理')

const { ids4 } = useApi()
const page = reactive<ClientsParams>({
  page: 1,
  pageSize: 10,
  searchText: '',
  ownerName: '',
})
const { data, execute } = ids4.getClients(page, { immediate: false })
const { data: owners, onFetchResponse, execute: update } = ids4.getOwners()
const tableInstance = ref<TableInstance>()
const isToggleAll = ref(false)
const { data: scopeModules } = ids4.apiSourceModules()
const scopes = computed(() => scopeModules.value?.map((item) => item.scopes).flat())

onFetchResponse(() => {
  page.ownerName = page.ownerName || owners.value?.[0].ownerName || ''
  execute()
})

function handleAdd() {
  const newWindow = navigateTo({ name: 'client' }, { open: { target: '_blank' } })
  newWindow?.addEventListener('create-client-success', () => {
    ElNotification({
      type: 'success',
      message: '新建客户端成功',
    })
    update()
    window.focus()
  })
}
async function handleUpdate(row: Client) {
  const response = await ids4.updateClient(row)
  if (response.statusCode.value === 200) {
    ElNotification({
      type: 'success',
      message: '更新客户端成功',
    })
    update()
  } else {
    ElNotification({
      type: 'error',
      message: response.error.toString(),
    })
  }
}
function handleUpdateClient(id: string) {
  const newWindow = navigateTo(
    { name: 'update_client', params: { id: id } },
    { open: { target: '_blank' } },
  )
  newWindow?.addEventListener('create-client-success', () => {
    ElNotification({
      type: 'success',
      message: '更新客户端成功',
    })
    update()
    window.focus()
  })
}

function handleToggleAll() {
  tableInstance.value?.toggleAllSelection()
  isToggleAll.value = !isToggleAll.value
}
function handleBatchDelete() {
  const rows = tableInstance.value?.getSelectionRows() as Client[]
  if (!rows.length) {
    return ElNotification({
      type: 'warning',
      message: '请选择要删除的行',
    })
  }
  ElMessageBox.confirm('确定要删除当前选中的所有数据吗', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const response = await ids4.deleteClientIds(
      rows
        .map((item) => item.id)
        .filter(Boolean)
        .join(','),
    )
    if (response.statusCode.value === 200) {
      update()
      ElNotification({
        type: 'success',
        message: `删除${rows.length}条数据成功`,
      })
    }
  })
}
function handleLog(clientId: string) {
  navigateTo({ name: 'client_log', query: { clientId } }, { open: { target: '_blank' } })
}
</script>

<template>
  <el-container class="h-full">
    <el-aside class="bg-white">
      <el-menu class="h-full py-20px" :default-active="page.ownerName">
        <el-menu-item
          v-for="item in owners"
          :key="item.ownerName"
          class="font-500!"
          style="--el-menu-base-level-padding: 50px"
          :index="item.ownerName"
          @click="page.ownerName = item.ownerName"
        >
          <el-icon>
            <div i-material-symbols-light:deployed-code-sharp></div>
          </el-icon>
          {{ item.ownerName }}({{ item.count }})
        </el-menu-item>
      </el-menu>
    </el-aside>
    <el-main>
      <div class="bg-white h-full rounded-8px p-20px">
        <div class="mb-20px flex">
          <div>
            <el-button type="primary" @click="handleAdd">添加 </el-button>
            <el-button plain @click="handleToggleAll">
              {{ isToggleAll ? '全不选' : '全选' }}
            </el-button>
            <el-button plain @click="handleBatchDelete">批量删除</el-button>
          </div>
          <div flex-1></div>
          <div>
            <el-input
              :suffix-icon="PhMagnifyingGlassLight"
              placeholder="搜索"
              v-model="page.searchText"
            />
          </div>
        </div>

        <el-table
          :data="data?.clients"
          style="width: 100%"
          :row-style="{ height: '60px' }"
          ref="tableInstance"
          @select-all="
            (selection) => {
              if (selection.length === data?.clients.length) {
                isToggleAll = true
              } else {
                isToggleAll = false
              }
            }
          "
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="clientName" label="应用名称" />
          <el-table-column prop="clientId" label="客户端ID" />
          <el-table-column prop="allowedScopes" label="接入模块">
            <template #default="{ row }">
              <el-tag
                v-for="item in row.allowedScopes"
                :key="item"
                size="large"
                class="mr-6px my-4px"
              >
                {{ findScope(scopes, item).value }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="ownerName" label="所属单位" width="120" />
          <el-table-column prop="enabled" label="是否启用" width="100">
            <template #default="{ row }">
              <el-switch v-model="row.enabled" size="large" @change="handleUpdate(row)" />
            </template>
          </el-table-column>
          <el-table-column prop="created" label="创建日期" width="180">
            <template #default="{ row }">
              {{ dayjs(row.created).format('YYYY-MM-DD HH:mm') }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleLog(row.clientId)">日志</el-button>
              <el-button type="primary" link @click="handleUpdateClient(row.id)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          class="mt-20px float-right"
          background
          layout="prev, pager, next"
          :total="data?.totalCount"
          v-model:page-size="page.pageSize"
          v-model:current-page="page.page"
        />
      </div>
    </el-main>
  </el-container>
</template>

<style scoped>
.el-menu-item.is-active {
  background-color: white;
}
</style>
