<script setup lang="ts">
import { useAuth } from 'vue-oidc-provider'

// 开放路由，不需要身份认证
const publicRoute = ['/retrieve_password']
const { isAuthenticated, isLoading, signinRedirect } = useAuth()

watch([isLoading, isAuthenticated], async ([isLoading, isAuthenticated]) => {
  if (!isLoading && !isAuthenticated) {
    if (publicRoute.some((item) => location.pathname.includes(item))) return
    signinRedirect()
  }
})
</script>

<template>
  <el-config-provider size="large">
    <el-main class="bg-#f5f7fa" style="--el-main-padding: 0">
      <RouterView v-slot="{ Component }">
        <template v-if="Component">
          <KeepAlive>
            <Suspense>
              <!-- 主要内容 -->
              <component :is="Component"></component>

              <!-- 加载中状态 -->
              <template #fallback> 正在加载... </template>
            </Suspense>
          </KeepAlive>
        </template>
      </RouterView>
    </el-main>
  </el-config-provider>
</template>

<style scoped></style>
