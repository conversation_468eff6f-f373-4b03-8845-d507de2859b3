<script lang="ts" setup>
import type { M4, M17DataParams } from '~performance/api/performance'

const route = useRoute()
const query = route.query as Object as M17DataParams & { name: string }
const { performance } = useFetchApi()
const params = ref<M17DataParams>({
  startDate: query.startDate,
  endDate: query.endDate,
  departmentId: query.departmentId,
  pageIndex: 1,
  pageSize: 10,
})
const { data } = performance.getM17Data(params)

function handleRowClick(row: M4) {
  navigateTo(appBaseURL.value + row.key, {
    open: {
      target: isMobileSize.value ? '_self' : '_blank',
    },
  })
}
</script>

<template>
  <div class="max-sm:px-2 md:px-14 lg:px-20 xl:px-30 2xl:px-45">
    <Title size="17px">{{ decodeURI(query.name) }}</Title>

    <div class="p-10px bg-white rounded-10px">
      <el-table
        :data="data?.data"
        style="width: 100%"
        show-overflow-tooltip
        @row-click="handleRowClick"
      >
        <el-table-column prop="name" label="应用名称" />
        <!-- <el-table-column prop="value" label="办理次数" width="100" align="center" /> -->
      </el-table>

      <el-pagination
        class="mt-20px"
        :page-sizes="[10, 15, 20, 25, 30, 40, 50]"
        layout="prev, pager, next, total, sizes"
        :total="data?.count"
        v-model:page-size="params.pageSize"
        v-model:current-page="params.pageIndex"
      />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
