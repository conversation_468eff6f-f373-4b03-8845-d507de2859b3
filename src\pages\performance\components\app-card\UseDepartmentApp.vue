<script lang="ts" setup>
import type { M6 } from '~performance/api/performance'

const router = useRouter()
const { performance } = useFetchApi()
const { data } = performance.statisticsData<'m6'>('m6')
function handleRowClick(row: M6) {
  router.push({
    name: 'department_view',
    query: {
      name: encodeURI('部门应用使用情况'),
      departmentId: row.key,
      startDate: performance.statisticsDate.value.startDate,
      endDate: performance.statisticsDate.value.endDate,
    },
  })
}
</script>

<template>
  <Card title="部门应用使用情况" style="--max-height-content: 380px" border>
    <el-table
      :data="data?.data"
      style="width: 100%"
      show-overflow-tooltip
      @row-click="handleRowClick"
    >
      <el-table-column prop="name" label="部门名称" />
      <el-table-column prop="value" label="应用使用次数" width="120" align="center" />
    </el-table>
  </Card>
</template>

<style lang="less" scoped>
@import '~performance/styles/table.less';
</style>
