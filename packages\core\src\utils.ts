import { isAbsolute, resolve } from 'path'
import { type ViteUnPageConfig } from './config'
import FastGlob from 'fast-glob'
import type { Choice } from 'prompts'
import { mergeConfig, type InlineConfig } from 'vite'

export function mergeViteUnPageConfig(
  rootConfig: Record<string, any>,
  userConfig: Record<string, any>,
  entry: ChoiceValue,
  mode: 'development' | 'production' = 'development',
) {
  const pageName = getLastPath(entry.path)

  const config = mergeConfig(rootConfig, userConfig) as ViteUnPageConfig
  const vitePlugin = config.vitePlugin
    ? Object.keys(config.vitePlugin)
        .filter((key) => !config.vitePlugin![key]?.disabled && config.vitePlugin![key]?.plugin)
        .map((key) => config.vitePlugin![key]!.plugin)
    : []
  config.plugins?.push(...vitePlugin)

  if (userConfig.base) {
    config.base = isAbsolute(userConfig.base)
      ? userConfig.base
      : resolve(rootConfig.base, userConfig.base)
  } else {
    config.base = resolve(rootConfig.base, `./${pageName}`) + '/'
  }

  const inlineConfig: InlineConfig = {
    mode,
    root: entry.path,
    define: {
      __APP_BASE__: JSON.stringify(config.base),
      __APP_MODE__: JSON.stringify(mode),
      __APP_PROD__: mode === 'development' ? false : true,
    },
    publicDir: resolve(entry.path, './public'),
    configFile: false,
    build: {
      outDir: resolve(process.cwd(), `./dist${config.base}`),
      emptyOutDir: true,
    },
    resolve: {
      alias: [
        { find: '@/', replacement: `${resolve(process.cwd(), './src')}/` },
        { find: '~/', replacement: `${resolve(process.cwd(), './src')}/` },
        { find: `~${pageName}/`, replacement: `${entry.path}/` },
        { find: `*${pageName}/`, replacement: `${entry.path}/` },
      ],
    },
  }

  return mergeConfig(config, inlineConfig)
}

export type ChoiceValue = { name: string; path: string }

export async function parseUnPages(answer?: string, isExample: boolean = false): Promise<Choice[]> {
  answer = answer ? answer : '*'
  const source = isExample ? `./src/examples/${answer}` : `./src/pages/${answer}`

  const options = await FastGlob.glob(source, {
    cwd: process.cwd(),
    absolute: true,
    onlyDirectories: true,
    deep: 1,
    stats: true,
  })

  return options.map((item) => ({ title: item.name, value: { name: item.name, path: item.path } }))
}

export interface GetLastPathOptions {
  markDirectories?: boolean
}

export const getLastPath = (path: string, options: GetLastPathOptions = {}) => {
  const { markDirectories = false } = options
  const pathArr = path.split('/')
  const len = pathArr.length
  const lastIndex = len - 1
  const lastPath = pathArr[lastIndex]

  if (markDirectories) {
    return '/' + lastPath
  }
  return lastPath
}
