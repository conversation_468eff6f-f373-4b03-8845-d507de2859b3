import type {
  ApiSourceModule,
  Client,
  ClientsParams,
  GrantLogs,
  GrantLogsParams,
  PasswordPolicy,
  UserProperties,
} from './types'
import type { UseFetchOptions } from '@vueuse/core'
import type { MaybeRef, MaybeRefOrGetter } from 'vue'

export const baseURL = remoteConfig.hosts.gateway + '/ids4api'

export * from './types'

const http = createService(baseURL, {
  options: {
    afterFetch(ctx) {
      if (!ctx.response.status.toString().startsWith('2')) {
        ElNotification({
          type: 'error',
          title: '请求 Error',
          message: ctx.data.message ? ctx.data.message : 'API请求错误,请检查网络',
        })
      }
      return ctx
    },
  },
})

enum API {
  API_RESOURCES = '/api/ApiResources',
  API_RESOURCES_ID_SCOPE = '/api/ApiResources',
  CLIENT_CREATE = '/api/Clients/Create',
  CLIENTS = '/api/Clients/Clients',
  GET_OWNERS = '/api/Clients/Owners',
  CLIENT_DETAIL = '/api/Clients',
  UPDATE_CLIENT = '/api/Clients/Update',
  DELETE_CLIENT_IDS = '/api/Clients/ByIds',
  MODULES = '/api/ApiScopes/Modules',
  IDENTITY_PASSWORD = '/api/Users/<USER>/Password',
  GET_USER_PROPERTIES = '/api/NewOchart/GetUserProperties',
  GET_GRANT_LOGS = '/api/IduoLoginLog/GetGrantLogs',
}

export function createClient(client: Client) {
  return http(API.CLIENT_CREATE).post(client).json()
}

export function getClients(params: MaybeRefOrGetter<ClientsParams>, options: UseFetchOptions = {}) {
  return http(
    computed(() => API.CLIENTS + paramsSerializer(toValue(params) || {})),
    options,
  )
    .get()
    .json<{
      pageSize: number
      totalCount: number
      clients: Client[]
    }>()
}

export function getOwners() {
  return http(API.GET_OWNERS).get().json<Array<{ ownerName: string; count: number }>>()
}

export function getClientDetail(id: MaybeRefOrGetter<string>) {
  return http(computed(() => `${API.CLIENT_DETAIL}/${toValue(id)}/Details`))
    .get()
    .json<Client>()
}

export function updateClient(data: Client) {
  return http(API.UPDATE_CLIENT).post(data).json()
}

export function deleteClientIds(ids: MaybeRefOrGetter<string>) {
  return http(computed(() => `${API.DELETE_CLIENT_IDS}/${toValue(ids)}`))
    .delete()
    .json()
}

export function apiSourceModules() {
  return http(API.MODULES).get().json<ApiSourceModule[]>()
}

export function getIdentityPassword() {
  return http(API.IDENTITY_PASSWORD).get().json<PasswordPolicy>()
}

export function updateIdentityPassword(body: PasswordPolicy) {
  return http(API.IDENTITY_PASSWORD).put(body).json()
}

export function getUserProperties() {
  return http(API.GET_USER_PROPERTIES).get().json<APIResult<UserProperties[]>>()
}

export function getGrantLogs(params: MaybeRef<GrantLogsParams>) {
  return http(API.GET_GRANT_LOGS).post(params).json<APIResult<GrantLogs[]>>()
}
