<script lang="ts" setup>
import { OpenTypeEnum, type MenuBody } from '@unpages/init'

const props = defineProps<{
  route: MenuBody
}>()

const { route } = toRefs(props)
const isSubMenu = computed(() => route.value.subMenus.length)
const to = computed(() =>
  route.value.url.startsWith('http') ? '/iframe?src=' + route.value.url : route.value.url,
)

const openTypeFn = {
  [OpenTypeEnum.视图容器]: () => navigateTo(to.value),
  [OpenTypeEnum.框架中]: () => navigateTo(to.value),
  [OpenTypeEnum.新窗口]: () => navigateTo(to.value, { open: { target: '_blank' } }),
  [OpenTypeEnum.当前窗口]: () => navigateTo(to.value, { open: { target: '_self' } }),
  [OpenTypeEnum.自定义]: () => navigateTo(to.value),
}

function open() {
  const { openType } = route.value
  openTypeFn[openType] && openTypeFn[openType]()
}
</script>

<template>
  <template v-if="isSubMenu">
    <el-sub-menu :index="route.url">
      <template #title>
        <el-icon size="20" v-if="route.iconName">
          <Icon :icon="route.iconName" />
        </el-icon>
        <span>{{ route.name }}</span>
      </template>
      <template #default>
        <MenuItem v-for="item in route.subMenus" :key="item.id" :route="item" />
      </template>
    </el-sub-menu>
  </template>
  <template v-else>
    <el-menu-item :index="route.url" @click="open">
      <template #title>
        <span>{{ route.name }}</span>
      </template>
      <template #default>
        <el-icon size="20" v-if="route.iconName">
          <Icon :icon="route.iconName" />
        </el-icon>
      </template>
    </el-menu-item>
  </template>
</template>
