import { useIdpTag } from './hooks'
import { createOidc, useOidcStore, type VueOidcSettings } from 'vue3-oidc'

const { state } = useOidcStore()

const oidcSettings: VueOidcSettings = {
  ...window.remoteConfig.oidcSettings,
  redirect_uri: window.location.href,
  acr_values: useIdpTag(),
  loadUserInfo: true,
  automaticSilentRenew: true,
  monitorSession: true,
  silent_redirect_uri: location.origin + __APP_BASE__ + '/auth/silent-renew-oidc.html',
  onSigninRedirectCallback(user) {
    location.href = unref(state).redirect_uri || window.location.pathname
  },
}

createOidc({
  oidcSettings: oidcSettings, //your oidc settings
  auth: true, //if auth is true,will auto authenticate
  events: {}, //your oidc customization callback events
})
