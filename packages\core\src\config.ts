import { resolve } from 'path'
import { VantResolver } from '@vant/auto-import-resolver'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import type { UserConfig } from 'vite'

type VitePluginOptions = {
  disabled?: boolean
}

type VitePlugin = {
  'unplugin-auto-import': VitePluginOptions & {
    plugin?: ReturnType<typeof AutoImport>
  }
  'unplugin-vue-components': VitePluginOptions & {
    plugin?: ReturnType<typeof Components>
  }
  [key: string]: VitePluginOptions & {
    plugin?: any
  }
}

export interface UnPageConfig {
  /**
   * 忽略打包
   * default false
   */
  notBuild?: boolean
  /**
   * 只有root根目录配置全局前置base生效，pages目录下base规则是 默认(root + 文件夹名 (/pages/oauth)) 如果是绝对路径(取用户配置下的base) 如果是相对路径(root + userBase)
   * default /pages
   */
  base?: string
  /**
   * vite plugin on demand load
   */
  vitePlugin?: Partial<VitePlugin>
}

export type ViteUnPageConfig = UnPageConfig & UserConfig

export const defineUnPageConfig = (config: UnPageConfig) => config

const inlineConfig: Required<UnPageConfig> = {
  notBuild: false,
  base: '/pages',
  vitePlugin: {
    'unplugin-auto-import': {
      plugin: AutoImport({
        resolvers: [ElementPlusResolver()],
        vueTemplate: true,
        imports: ['vue', '@vueuse/core', 'pinia', 'vue-router'],
        eslintrc: {
          enabled: true, // Default `false`
          filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
          globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },
        dts: resolve(process.cwd(), './auto-imports.d.ts'),
      }),
    },
    'unplugin-vue-components': {
      plugin: Components({
        resolvers: [VantResolver({}), ElementPlusResolver()],
        dts: resolve(process.cwd(), './components.d.ts'),
      }),
    },
  },
}

/**
 * root unpage.config.ts is config
 */
export const inlineRootUnPageConfig: Required<UnPageConfig> = {
  ...inlineConfig,
}

/**
 * pages unpage.config.ts is config
 */
export const inlineUnPageConfig: Required<UnPageConfig> = {
  ...inlineConfig,
  base: '',
}
