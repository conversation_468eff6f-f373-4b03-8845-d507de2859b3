<script lang="ts" setup>
import type { PasswordPolicy } from '~identity-authentication/api/ids4/types'

useTitle('密码策略')

const { ids4 } = useApi()
const { data, statusCode, onFetchResponse, execute } = ids4.getIdentityPassword()
const form = ref<PasswordPolicy>({
  requiredLength: 8,
  requireDigit: true,
  requireUppercase: true,
  requireLowercase: true,
  requireNonAlphanumeric: true,
})

onFetchResponse(() => {
  if (statusCode.value === 200) {
    form.value = data.value!
  }
})

async function confirm() {
  const { statusCode } = await ids4.updateIdentityPassword(form.value)
  if (statusCode.value === 200) {
    ElNotification.success('保存成功')
    execute()
  } else {
    ElNotification.error('保存失败')
  }
}

function resetDefault() {
  ElMessageBox.confirm('确定要重置密码策略吗', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(async () => {
    const { statusCode } = await ids4.updateIdentityPassword(remoteConfig.identity_password)
    if (statusCode.value === 200) {
      ElNotification.success('重置成功')
      execute()
    } else {
      ElNotification.error('重置失败')
    }
  })
}
</script>

<template>
  <MainTemplate>
    <h2 class="mt-10px mb-20px color-gray-7">密码策略</h2>

    <el-form :model="form" label-position="left" label-width="auto" size="large">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最小密码长度" prop="requiredLength">
            <el-input-number v-model="form.requiredLength" :min="1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="必须包含数字" prop="requireDigit">
            <el-switch v-model="form.requireDigit" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="必须包含小写字母" prop="requireLowercase">
            <el-switch v-model="form.requireLowercase" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="必须包含大写字母" prop="requireUppercase">
            <el-switch v-model="form.requireUppercase" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="必须包含特殊字符" prop="requireNonAlphanumeric">
            <el-switch v-model="form.requireNonAlphanumeric" />
          </el-form-item>
        </el-col>

        <el-col :span="24" class="mt-20px">
          <el-button type="primary" @click="confirm">确认修改</el-button>
          <el-button @click="resetDefault">恢复默认</el-button>
        </el-col>
      </el-row>
    </el-form>
  </MainTemplate>
</template>

<style scoped></style>
