<script lang="ts" setup>
import type { GrantLogsParams } from '../../api/ids4'
import PhMagnifyingGlassLight from '~icons/ph/magnifying-glass-light'

useTitle('认证日志')

const { ids4 } = useApi()
const params = ref<GrantLogsParams>({
  pageIndex: 1,
  pageSize: 10,
  keyWord: '',
  sortField: '',
  sortType: 'DESC',
  totalCount: 0,
  clientId: '',
  startTime: '',
  endTime: '',
})
const { data } = ids4.getGrantLogs(params)
</script>

<template>
  <MainTemplate>
    <h2 class="mt-10px mb-20px color-gray-7">认证日志</h2>

    <el-form inline>
      <el-form-item label="客户端ID">
        <el-input v-model="params.clientId" style="width: 240px" placeholder="搜索" />
      </el-form-item>
      <el-form-item label="认证时间">
        <el-date-picker
          :modelValue="[params.startTime, params.endTime]"
          @update:modelValue="
            ([startTime, endTime]) => ((params.startTime = startTime), (params.endTime = endTime))
          "
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="h-32px!"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm"
        />
      </el-form-item>
      <el-form-item label="关键字">
        <el-input
          v-model="params.keyWord"
          style="width: 240px"
          placeholder="搜索"
          :suffix-icon="PhMagnifyingGlassLight"
        />
      </el-form-item>
    </el-form>

    <el-table
      :data="data?.data"
      :row-style="{ height: '50px' }"
      style="width: 100%"
      show-overflow-tooltip
    >
      <el-table-column label="用户名" prop="grantNickName" width="150" />
      <el-table-column label="服务器IP" prop="grantUserIp" width="150" />
      <el-table-column label="认证时间" prop="grantTime" />
      <el-table-column label="目标系统" prop="clientName" />
      <el-table-column label="代理信息">
        <template #default="{ row }">
          {{ row.agentInfo.xOriginalFor }}
        </template>
      </el-table-column>
      <el-table-column label="认证结果" width="150">
        <template #default="{ row }">
          <el-tag size="large" :type="row.grantResult.code === 1 ? 'success' : 'danger'">
            {{ row.grantResult.code === 1 ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="失败原因">
        <template #default="{ row }">
          {{ row.grantResult.code === 1 ? '' : row.grantResult.message }}
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      class="my-20px"
      background
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 30, 40]"
      :total="data?.count"
      v-model:page-size="params.pageSize"
      v-model:current-page="params.pageIndex"
    />
  </MainTemplate>
</template>

<style scoped></style>
