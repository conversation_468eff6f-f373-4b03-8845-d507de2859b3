@font-face {
  font-family: "PingFang";
  src: url("./assets/PingFang.ttf");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: "DIN";
  src: url("./assets/DIN-bold.ttf");
  font-weight: bold;
  font-style: normal;
}

:root {
  font-family: "PingFang";
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  background-color: #f7f8fe;
  --el-color-primary: #3464e0;
  --el-color-primary-light-3: #4671dc;
  --el-color-primary-light-5: #6385d9;
  --el-color-primary-light-7: #7a95d7;
  --el-color-primary-light-8: #9aacda;
  --el-color-primary-light-9: #b5bfda;
  --el-color-primary-dark-2: #214cb9;
}

div {
  box-sizing: border-box;
}

#app {
  height: 100%;
}
.el-table .el-table__body-wrapper .el-table__body .el-table__row:hover {
  cursor: pointer;
  color: var(--el-color-primary);
}

.el-pagination {
  flex-wrap: wrap;
}
