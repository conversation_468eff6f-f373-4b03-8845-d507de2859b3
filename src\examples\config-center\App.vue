<script setup lang="ts">
import OidcUser from './views/OidcUser.vue'
import { presetOidcSettings } from '@/oidc/presetOidcSettings'
import { ref } from 'vue'
import { AuthProvider, type AuthProviderProps } from 'vue-oidc-provider'

const message = ref('OAuth')

const oidcSettings = {
  ...presetOidcSettings,
  redirect_uri: window.location.origin,
  onSigninCallback: (user) => {
    window.history.replaceState({}, document.title, window.location.pathname)
  },
} satisfies AuthProviderProps
</script>

<template>
  <AuthProvider :options="oidcSettings">
    <h1>Hello {{ message }}</h1>
    <OidcUser></OidcUser>
  </AuthProvider>
</template>

<style scoped></style>
