<script lang="ts" setup>
import type { ChangePassword } from '../api/ids4/types'
import type BasicUserInfo from '../components/BasicUserInfo.vue'
import type Identity from '../components/Identity.vue'
import {
  checkRetrievePasswordToken,
  clearRetrievePasswordToken,
  isPasswordValid,
  type UserBasic,
} from '../utils'
import { useResetRef } from '@unpages/utils'
import MaterialSymbolsCounter1OutlineRounded from '~icons/material-symbols/counter-1-outline-rounded'
import MaterialSymbolsCounter2OutlineRounded from '~icons/material-symbols/counter-2-outline-rounded'
import MaterialSymbolsCounter3OutlineRounded from '~icons/material-symbols/counter-3-outline-rounded'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

const { ids4 } = useApi()
const [active, resetActive] = useResetRef(1)
const basicUserInfoRef = ref<InstanceType<typeof BasicUserInfo>>()
const identityRef = ref<InstanceType<typeof Identity>>()
const userBasic = ref<UserBasic>()
const formRef = ref<FormInstance>()
const [form, resetForm] = useResetRef<ChangePassword>({
  newPassword: '',
  confirmPassword: '',
})
// const { data: identityPassword } = await ids4.getIdentityPassword()
const rules = reactive<FormRules<ChangePassword>>({
  newPassword: {
    required: true,
    validator: (rule, value, callback) => {
      // const { valid, message } = isPasswordValid(value, identityPassword.value!)
      if (value === '') {
        callback(new Error('新密码不能为空'))
      } else {
        callback()
      }
    },
    trigger: 'blur',
  },
  confirmPassword: {
    required: true,
    validator: (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次确认新密码'))
      } else if (value !== form.value.newPassword) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    },
    trigger: 'blur',
  },
})

function init() {
  const token = checkRetrievePasswordToken()
  if (token) {
    active.value = 3
  }
}
async function next() {
  if (active.value === 1) {
    basicUserInfoRef.value
      ?.validate()
      .then((data) => {
        userBasic.value = data
        active.value++
      })
      .catch((err) => {
        ElMessage.error(err)
      })
  } else if (active.value === 2) {
    identityRef.value
      ?.validate()
      .then((data) => {
        active.value++
      })
      .catch((err) => {
        ElMessage.error(err)
      })
  }
}
function back() {
  active.value--
}
function clearAllForm() {
  basicUserInfoRef.value?.clearForm()
  identityRef.value?.clearForm()
  resetForm()
  formRef.value?.clearValidate()
}
async function confirm() {
  const token = checkRetrievePasswordToken()
  if (token === false) {
    ElMessage.error('身份已失效，请重新验证！')
    clearAllForm()
    resetActive(1)
    clearRetrievePasswordToken()
    return
  }
  const { data } = await ids4.changePassword(form.value, token)
  if (data.value?.code === 1 && data.value.data === true) {
    ElMessage.success('重制密码成功')
    clearAllForm()
    resetActive(1)
    clearRetrievePasswordToken()
  } else {
    ElMessage.error(data.value?.message)
  }
}
onMounted(() => {
  init()
})
</script>

<route lang="yaml">
name: 'retrieve_password'
</route>

<template>
  <div class="flex justify-center">
    <main class="w-800px">
      <el-steps class="mt-10px mb-50px sm:text-12px" :space="200" :active="active" simple>
        <el-step title="基础信息验证" :icon="MaterialSymbolsCounter1OutlineRounded" />
        <el-step title="身份验证" :icon="MaterialSymbolsCounter2OutlineRounded" />
        <el-step title="重制密码" :icon="MaterialSymbolsCounter3OutlineRounded" />
      </el-steps>

      <div class="px-8% my-4">
        <BasicUserInfo v-show="active === 1" ref="basicUserInfoRef" />
        <Identity v-show="active === 2" ref="identityRef" :userBasic="userBasic" />
        <el-form
          v-show="active === 3"
          :model="form"
          label-position="top"
          label-width="auto"
          ref="formRef"
          :rules="rules"
        >
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="form.newPassword"
              type="password"
              autocomplete="off"
              placeholder="请输入新密码"
              show-password
            />
          </el-form-item>
          <el-form-item label="重复密码" prop="confirmPassword">
            <el-input
              v-model="form.confirmPassword"
              type="password"
              autocomplete="off"
              placeholder="请再次输入新密码"
              show-password
            />
          </el-form-item>
        </el-form>

        <el-row :gutter="10">
          <el-col v-if="active !== 1" :span="12">
            <el-button type="primary" plain round w-full mt-20px @click="back">上一步</el-button>
          </el-col>
          <el-col v-if="active < 3" :span="active === 1 ? 24 : 12">
            <el-button type="primary" round w-full mt-20px @click="next">下一步</el-button>
          </el-col>
          <el-col v-if="active === 3" :span="12">
            <el-button type="primary" round w-full mt-20px @click="confirm">提 交</el-button>
          </el-col>
        </el-row>
      </div>
    </main>
  </div>
</template>

<style lang="less" scoped>
@media (max-width: 640px) {
  :deep(.el-step.is-simple) .el-step__title {
    font-size: 12px;
  }
}
</style>
