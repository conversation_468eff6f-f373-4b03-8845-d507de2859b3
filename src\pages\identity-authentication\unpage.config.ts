import { resolve } from 'path'
import { defineUnPageConfig } from '@unpages/core'
import { VantResolver } from '@vant/auto-import-resolver'
import AutoImport from 'unplugin-auto-import/vite'
import IconsResolver from 'unplugin-icons/resolver'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'

export default defineUnPageConfig({
  vitePlugin: {
    'unplugin-vue-components': {
      plugin: Components({
        resolvers: [
          VantResolver({}),
          ElementPlusResolver(),
          IconsResolver(),
          (componentName) => {
            if (componentName === 'Icon') {
              return { name: 'Icon', from: '@iconify/vue' }
            }
          },
        ],
        dts: resolve(process.cwd(), './components.d.ts'),
        dirs: ['./components'],
        directoryAsNamespace: true,
        types: [
          {
            from: '@iconify/vue',
            names: ['Icon'],
          },
        ],
      }),
    },
    'unplugin-auto-import': {
      plugin: AutoImport({
        resolvers: [ElementPlusResolver()],
        vueTemplate: true,
        imports: ['vue', '@vueuse/core', 'pinia', 'vue-router'],
        eslintrc: {
          enabled: true, // Default `false`
          filepath: resolve(process.cwd(), './.eslintrc-auto-import.json'), // Default `./.eslintrc-auto-import.json`
          globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },
        dts: resolve(process.cwd(), './auto-imports.d.ts'),
        dirs: ['./composables'],
      }),
    },
  },
})
