import type { RoleParams } from './types'
import type { MenuBody } from '@unpages/init'

export * from './types'

export const baseURL = remoteConfig.hosts.gateway + '/app'

const http = createService(baseURL)

enum API {
  GET_ROLES = '/api/Menus/getByRoles',
}

export function getRoles(query: RoleParams) {
  return http(API.GET_ROLES + paramsSerializer(query))
    .get()
    .json<APIResult<MenuBody[]>>()
}
