<script lang="ts" setup>
import type { ScopeCount } from '../api/log/types'
import * as echarts from 'echarts'

const { log } = useApi()
const { data, onFetchResponse } = log.getCountByProject()

onFetchResponse(() => {
  const result = data.value?.data.map((item) => ({
    name: item.name,
    value: item.total,
  }))

  const option: echarts.EChartsOption = {
    tooltip: {},
    title: {
      show: true,
    },
    legend: {
      data: result?.map((item) => item.name),
    },
    xAxis: {
      name: '模块名称',
      data: result?.map((item) => item.name),
      show: true,
      type: 'category',
    },
    yAxis: {
      type: 'value',
      name: '调用次数',
      nameGap: 40,
      axisLine: {
        show: true,
      },
      show: true,
    },
    series: result?.map((item, index) => ({
      data: Array.from({ length: result.length }, (_, i) => (index === i ? item.value : 0)),
      type: 'bar',
      stack: 'stack',
      name: item.name,
    })),
  }

  nextTick(() => {
    const el = document.getElementById('echarts')
    const echart = echarts.init(el)
    window.onresize = () => {
      echart?.resize()
    }
    echart && echart.setOption(option)
  })
})
function handleSuccessClick(row: ScopeCount) {
  navigateTo(
    { name: 'client_log', query: { project: row.name, status: 'success' } },
    { open: { target: '_blank' } },
  )
}
</script>

<template>
  <MainTemplate>
    <h2 class="mt-10px mb-20px color-gray-7">统计</h2>

    <div id="echarts" class="h-60% w-full min-h-600px"></div>

    <div class="h-30% px-60px py-20px bg-#fafafa">
      <el-table :data="data?.data" show-overflow-tooltip border>
        <el-table-column label="模块名称" prop="name" />
        <el-table-column label="总数" prop="total">
          <template #default="{ row }">
            <el-link @click="handleSuccessClick(row)">{{ row.total }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="成功次数" prop="successCount">
          <template #default="{ row }">
            <el-link type="success" @click="handleSuccessClick(row)">
              {{ row.successCount }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="失败次数" prop="errorCount">
          <template #default="{ row }">
            <el-link type="danger" @click="handleSuccessClick(row)">{{ row.errorCount }}</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </MainTemplate>
</template>

<style lang="less" scoped></style>
