//https://github.com/hannoeru/vite-plugin-pages
import { isAuthMenu, menuConfig } from '@unpages/init'
import { initNavigate } from '@unpages/utils'
import routes from '~pages'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({ routes: routes, history: createWebHistory(import.meta.env.BASE_URL) })
console.log('vite-plugin-pages is routes: ', router.getRoutes())

initNavigate(router)

router.beforeEach((to, from, next) => {
  const routes = menuConfig.value.menus || []
  if (!routes?.length || to.meta.public || to.meta.hidden) return next()
  const [pass] = isAuthMenu(to.path)
  if (pass) {
    return next()
  }
  return next('/404')
})

export default router
