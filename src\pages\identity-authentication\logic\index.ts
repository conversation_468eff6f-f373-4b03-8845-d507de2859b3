import { AuthProtocol, type Client } from '../api/ids4'
import type { OCProps } from '@unpages/init'

export type TransformClient = Omit<Client, 'allowedRoles' | 'returnUserProperties'> & {
  allowedRoles: OCProps['selected']
  returnUserProperties: string[]
}

export function transformClients(clients: Client[] | undefined | null): TransformClient[] {
  if (!clients) return []
  return clients.map((client) => ({
    ...client,
    allowedRoles: JSON.parse(client.allowedRoles || '[]'),
    returnUserProperties: client.returnUserProperties?.split(',') || [],
    authProtocol: client.authProtocol || AuthProtocol.OAuth,
  }))
}

export function unTransformClient(client: TransformClient): Client {
  return {
    ...client,
    allowedRoles: JSON.stringify(client.allowedRoles),
    returnUserProperties: client.returnUserProperties?.join(','),
  }
}
