<script setup lang="ts">
defineProps<{
  text?: string;
  size?: string;
}>();
</script>

<template>
  <div class="title" :style="{ fontSize: size ? size : '16px' }">
    <slot>{{ text }}</slot>
  </div>
</template>

<style lang="less" scoped>
.title {
  --title-margin-x: 0;
  --title-margin-y: 14px;
  --title-color: #333333;
}
.title {
  font-family: "Courier New", Courier, monospace;
  color: var(--title-color);
  margin: var(--title-margin-y) var(--title-margin-x);
  font-weight: 500;
}
</style>
