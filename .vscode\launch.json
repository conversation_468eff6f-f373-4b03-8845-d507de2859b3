{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "tsx",
      "type": "node",
      "request": "launch",

      // Debug current file in VSCode
      "program": "${file}",

      /*
      Path to tsx binary
      Assuming locally installed
      */
      "runtimeExecutable": "${workspaceRoot}/node_modules/.bin/tsx",

      /*
      Open terminal when debugging starts (Optional)
      Useful to see console.logs
      */
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",

      // Files to exclude from debugger (e.g. call stack)
      "skipFiles": [
        // Node.js internal core modules
        "<node_internals>/**",

        // Ignore all dependencies (optional)
        "${workspaceFolder}/node_modules/**"
      ]
    }
  ]
}
