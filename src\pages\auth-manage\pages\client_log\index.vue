<script lang="ts" setup>
import type { ClientLog, ClientLogParams, ClientRequestLog } from '../../api/log/types'
import { Icon } from '@iconify/vue'
import { useResetRef } from '@unpages/utils'
import dayjs from 'dayjs'

useTitle('日志')
const route = useRoute()
const { log } = useApi()
const dialogVisible = ref(false)
const client_request = ref<ClientRequestLog>()
const [params, reset] = useResetRef<ClientLogParams>({
  userIdentifier: route.query?.clientId as string,
  project: route.query?.project as string,
  pageIndex: 1,
  pageSize: 10,
  clientIp: '',
  eventName: '',
  actionTimeStart: '',
  actionTimeEnd: '',
})
const data = ref<APIResult<ClientLog[]> | null>()

watchDebounced(
  params,
  async () => {
    const { data: _data } = await log.getClientLogs(params.value)
    data.value = _data.value
  },
  { immediate: true, deep: true, debounce: 300 },
)

async function handleRequest(id: string) {
  dialogVisible.value = true
  const { data } = await log.getClientRequest(id)
  console.log('data', data.value?.data)
  if (!data.value?.data) return
  try {
    client_request.value = data.value.data
  } catch (error) {}
}
</script>

<route lang="yaml">
name: client_log
meta:
  hidden: true
</route>

<template>
  <MainTemplate>
    <div class="flex justify-between items-center">
      <h2 class="mt-10px mb-20px color-gray-7">调用日志</h2>
      <div>
        <el-popover :width="450" placement="bottom-start" trigger="click">
          <template #reference>
            <el-button bg text title="筛选">
              <Icon icon="mingcute:filter-2-fill" />
            </el-button>
          </template>
          <div class="p-10px">
            <el-form :model="params" inline label-width="80px">
              <el-form-item label="应用名称" prop="userIdentifier" class="w-full">
                <el-input v-model="params.userIdentifier" placeholder="请输入应用名称" />
              </el-form-item>
              <el-form-item label="请求IP" prop="clientIp" class="w-full">
                <el-input v-model="params.clientIp" placeholder="请输入请求IP" />
              </el-form-item>
              <el-form-item label="接口名称" prop="eventName" class="w-full">
                <el-input v-model="params.eventName" placeholder="请输入接口名称" />
              </el-form-item>
              <el-form-item label="调用时间" class="w-full">
                <el-date-picker
                  :model-value="[params.actionTimeStart, params.actionTimeEnd]"
                  type="datetimerange"
                  value-format="YYYY-MM-DD HH:mm"
                  format="YYYY-MM-DD HH:mm"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  @update:model-value="
                    [params.actionTimeStart, params.actionTimeEnd] = $event || []
                  "
                  :teleported="false"
                />
              </el-form-item>
            </el-form>
            <el-button @click="reset()" class="float-right">重 置</el-button>
          </div>
        </el-popover>
      </div>
    </div>

    <el-table
      :data="data?.data"
      show-overflow-tooltip
      :row-style="{
        height: '60px',
      }"
    >
      <el-table-column label="应用名称" prop="userName" width="150" />
      <el-table-column label="请求IP" prop="clientIp" width="120" />
      <el-table-column label="接口名称" prop="eventName" width="200" />
      <el-table-column label="接口地址" prop="requestUrl" />
      <el-table-column label="调用时间" prop="actionTime" width="150">
        <template #default="{ row }">
          {{ dayjs(row.actionTime).format('YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>
      <el-table-column label="接口code" prop="returnResultCode" width="100">
        <template #default="{ row }">
          <el-tag :type="row.returnResultCode === 1 ? 'success' : 'danger'">
            {{ row.returnResultCode }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleRequest(row.id)">请求详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      class="mt-20px float-right"
      background
      layout="prev, pager, next"
      :total="data?.count"
      v-model:page-size="params.pageSize"
      v-model:current-page="params.pageIndex"
    />

    <el-dialog
      v-model="dialogVisible"
      title="请求详情"
      width="800px"
      style="--el-dialog-border-radius: 16px"
    >
      <div class="w-full text-1.2rem font-bold">
        {{ client_request?.clientBrowserInfo }}
      </div>
      <p class="w-full">{{ client_request?.actionData }}</p>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </MainTemplate>
</template>

<style lang="less" scoped></style>
