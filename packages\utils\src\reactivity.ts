import { cloneDeepWith } from 'lodash-es'
import { computed, ref, unref, type MaybeRef, type MaybeRefOrGetter } from 'vue'

export function isGetter<T>(source: MaybeRefOrGetter<T>): source is () => T {
  return typeof source === 'function'
}

export function toGetter<T>(source: MaybeRefOrGetter<T>) {
  if (isGetter(source)) {
    return source
  } else {
    return () => unref(source)
  }
}

export function toRefOrComputedRef<T>(source: MaybeRefOrGetter<T>) {
  if (isGetter(source)) {
    return computed(source)
  } else {
    return ref(source)
  }
}

export function MaybeRefOrGetterToRef<T>(source: MaybeRefOrGetter<T>) {
  if (isGetter(source)) {
    return ref(source())
  } else {
    return ref(source)
  }
}

export function useResetRef<T extends any>(value: MaybeRef<T>) {
  const _valueDefine = cloneDeepWith(value as any)
  const _value = isRef(value) ? value : ref(value)

  function reset(value?: T) {
    _value.value = value ? cloneDeepWith(value) : cloneDeepWith(_valueDefine)
  }

  return [_value, reset] as const
}
