import {
  globalConfigProviderIconPrefixRef,
  globalConfigProviderTagRef,
  globalConfigProviderThemeDarkVarsRef,
  globalConfigProviderThemeLightVarsRef,
  globalConfigProviderThemeRef,
  globalConfigProviderThemeVarsRef,
  globalConfigProviderThemeVarsScopeRef,
  globalConfigProviderZIndexRef,
} from './share'
import { ConfigProvider } from 'vant'
import { defineComponent } from 'vue'

export const VantConfigProviderPreset = defineComponent({
  name: 'VantConfigProviderPreset',
  setup(props, { slots }) {
    return () => (
      <ConfigProvider
        tag={globalConfigProviderTagRef.value}
        theme={globalConfigProviderThemeRef.value}
        zIndex={globalConfigProviderZIndexRef.value}
        themeVars={globalConfigProviderThemeVarsRef.value}
        themeVarsDark={globalConfigProviderThemeDarkVarsRef.value}
        themeVarsLight={globalConfigProviderThemeLightVarsRef.value}
        themeVarsScope={globalConfigProviderThemeVarsScopeRef.value}
        iconPrefix={globalConfigProviderIconPrefixRef.value}
      >
        {slots.default?.()}
      </ConfigProvider>
    )
  },
})
