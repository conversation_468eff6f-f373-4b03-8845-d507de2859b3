export const createUUID = (len: number = 36) => {
  if (len > 36) {
    throw new Error('Cannot be greater than 36')
  }
  return URL.createObjectURL(new Blob()).slice(-len)
}

export function createRandomID(length: number = 3): number {
  return Math.floor(Math.random() * 10 ** length)
}

export function createFileFormData(file: File) {
  const formData = new FormData()
  formData.append('File', file)
  formData.append('IsReName', false as unknown as string)
  formData.append('IsLanguage', false as unknown as string)
  formData.append('UploadType', 2 as unknown as string)
  formData.append('DirName', createUUID())
  formData.append('FileName', file.name)
  return formData
}

export function isJSON(data: string): boolean {
  try {
    if (typeof JSON.parse(data) === 'object') return true
  } catch {}
  return false
}

export function isWeChat(ua?: string) {
  const _ua = (ua || window?.navigator.userAgent).toLowerCase()
  return (
    _ua.match(/MicroMessenger/i)?.toString() === 'micromessenger' ||
    _ua.match(/_SQ_/i)?.toString() === '_sq_'
  )
}

export function isDingTalk(ua?: string) {
  const _ua = (ua || window?.navigator.userAgent).toLowerCase()
  return _ua.indexOf('dingtalk') > -1
}

export function getIdpValue(ua?: string) {
  if (isWeChat(ua)) return 'idp:Weixin'
  if (isDingTalk(ua)) return 'idp:DingTalk'
  return remoteConfig.oidcSettings.acr_values || ''
}

export function removeTag(html: string) {
  return html.replace(/<[^>]+>/g, '')
}
