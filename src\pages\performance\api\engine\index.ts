import type { MaybeRefOrGetter } from 'vue'

const baseURL = window.remoteConfig.hosts.gateway + '/engine'
const $fetch = createService(baseURL)

enum API {
  GetStatisticsTasks = '/api/InstanceTask/GetStatisticsTasks',
  AbortIncidentByInstanceId = '/api/ProcessManager/AbortIncidentByInstanceId',
}

export interface StatisticsTasksBody extends Page {
  processDefIds: string[]
  userId: string
  instanceStatus: Array<number | string | undefined>
  bizKey: string
  initiatorName: string
  processName: string
  stepName: string
  assigneeName: string
  summary: string
  startDate: string
  endDate: string
  shardingRuleDate: string
  isDue: boolean
  isActivate: boolean
  assignee: string
}

export interface StatisticsTasksResult {
  id: string
  processName: string
  progress: string
  applyDateTime: string
  instanceStatus: string
  summary: string
  taskId: string
  bizKey: string
  processVersion: number
  instanceId: string
  initiator: string
  initiatorName: string
  formUrl: string
  appUrl: string
  processDefId: string
  source: string
}

export function getStatisticsTasks(body?: MaybeRefOrGetter<Partial<StatisticsTasksBody>>) {
  return $fetch(API.GetStatisticsTasks).post(body).json<APIResult<StatisticsTasksResult[]>>()
}

export function abortIncidentByInstanceId(instanceId: string) {
  return $fetch(API.AbortIncidentByInstanceId + '?InstarnceId=' + instanceId)
    .get()
    .json<APIResult<{ successed: boolean; message: string }>>()
}
