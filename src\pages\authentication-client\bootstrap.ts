import 'uno.css'
import './style.css'
import '@duo-common/icon-v3/dist/style.css'

import App from './App.vue'
import router from './router'
import { setupDuoIcon } from '@duo-common/icon-v3'
import { createApp } from 'vue'
import { AuthProvider, type AuthProviderProps } from 'vue-oidc-provider'

const oidcSettings = {
  ...remoteConfig.oidcSettings,
  redirect_uri: window.location.href,
  onSigninCallback: () => {
    location.href = window.location.pathname
  },
} satisfies AuthProviderProps

setupDuoIcon({
  aliId: remoteConfig.aliId,
  textTip: false,
})

createApp(
  h(
    AuthProvider,
    {
      options: oidcSettings,
    },
    {
      default: () => h(App),
    },
  ),
)
  .use(router)
  .mount('#app')
