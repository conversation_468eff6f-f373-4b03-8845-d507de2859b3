import {
  type ConfigProviderProps,
  type ConfigProviderTheme,
  type ConfigProviderThemeVars,
  type ConfigProviderThemeVarsScope,
} from 'vant'
import { ref, type MaybeRefOrGetter } from 'vue'

export type ConfigProviderPropsKeys = keyof ConfigProviderProps

export type ConfigProviderPropsMaybeRefOrGetter = {
  [Key in ConfigProviderPropsKeys]: MaybeRefOrGetter<ConfigProviderProps[Key]>
} & {
  themeVars: MaybeRefOrGetter<ConfigProviderThemeVars>
  themeVarsDark: MaybeRefOrGetter<ConfigProviderThemeVars>
  themeVarsLight: MaybeRefOrGetter<ConfigProviderThemeVars>
}

export {
  type ConfigProviderTheme,
  type ConfigProviderThemeVars,
  type ConfigProviderThemeVarsScope,
} from 'vant'
export type ConfigProviderTag = ConfigProviderProps['tag']
export type ConfigProviderZIndex = ConfigProviderProps['zIndex']
export type ConfigProviderIconPrefix = ConfigProviderProps['iconPrefix']

export const globalConfigProviderTag: ConfigProviderTag = 'div'
export const globalConfigProviderTheme: ConfigProviderTheme = 'light'
export const globalConfigProviderZIndex: ConfigProviderZIndex = 2000
export const globalConfigProviderThemeVars: ConfigProviderThemeVars = {}
export const globalConfigProviderThemeDarkVars: ConfigProviderThemeVars = {}
export const globalConfigProviderThemeLightVars: ConfigProviderThemeVars = {}
export const globalConfigProviderThemeVarsScope: ConfigProviderThemeVarsScope = 'local'
export const globalConfigProviderIconPrefix: ConfigProviderIconPrefix = 'van-icon'

export const globalConfigProviderTagRef = ref(globalConfigProviderTag)
export const globalConfigProviderThemeRef = ref(globalConfigProviderTheme)
export const globalConfigProviderZIndexRef = ref(globalConfigProviderZIndex)
export const globalConfigProviderThemeVarsRef = ref(globalConfigProviderThemeVars)
export const globalConfigProviderThemeDarkVarsRef = ref(globalConfigProviderThemeDarkVars)
export const globalConfigProviderThemeLightVarsRef = ref(globalConfigProviderThemeLightVars)
export const globalConfigProviderThemeVarsScopeRef = ref(globalConfigProviderThemeVarsScope)
export const globalConfigProviderIconPrefixRef = ref(globalConfigProviderIconPrefix)
