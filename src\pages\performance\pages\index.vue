<script lang="ts" setup></script>

<template>
  <el-affix>
    <Section />
  </el-affix>
  <div class="max-sm:px-2 md:px-14 lg:px-20 xl:px-30 2xl:px-45">
    <Title size="17px">总体情况</Title>
    <div class="card-container">
      <PlatformCard />
      <WorkCard />
      <QueryCard />
    </div>

    <Title size="17px">运行情况</Title>
    <div class="card-container">
      <UseMatterCard />
      <DepartmentBuild />
      <UseDepartmentApp />
    </div>

    <Title size="17px">耗时情况</Title>
    <div class="card-container">
      <ProcessTimeCard />
      <PersonTimeCard />
      <DepartmentTimeCard />
    </div>

    <Title size="17px">待办情况</Title>
    <div class="card-container">
      <ProcessHandleCard />
      <PersonHandleCard />
      <DepartmentHandleCard />
    </div>

    <Title size="17px">超时情况</Title>
    <div class="card-container">
      <ProcessTimeoutCard />
      <PersonTimeoutCard />
      <DepartmentTimeoutCard />
    </div>
  </div>
</template>

<style lang="less" scoped>
.card-container {
  background-color: white;
  border-radius: 14px;
  padding: 18px;
  display: grid;
  grid-gap: 10px;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  grid-template-columns: 40% 29% 29%;
}

@media (max-width: 1280px) {
  .card-container {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

// @media (min-width: 640px) and (max-width: 1280px) {
//   .card-container {
//     grid-template-columns: repeat(2, minmax(0, 1fr));
//   }
// }
</style>
