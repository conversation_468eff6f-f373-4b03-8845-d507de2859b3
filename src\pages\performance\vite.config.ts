import { resolve } from 'path'
import Legacy from '@vitejs/plugin-legacy'
import Unocss from 'unocss/vite'
import { defineConfig } from 'vite'
import Inspect from 'vite-plugin-inspect'
import Pages from 'vite-plugin-pages'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    Legacy({
      targets: ['chrome >= 52'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
    }),
    Unocss(),
    Pages({
      dirs: resolve(__dirname, './pages'),
    }),
    Inspect(),
  ],
  server: {
    host: true,
  },
  esbuild: {
    target: 'es2015',
  },
  build: {
    rollupOptions: {
      input: [resolve(__dirname, 'index.html'), resolve(__dirname, 'auth/silent-renew-oidc.html')],
    },
  },
})
