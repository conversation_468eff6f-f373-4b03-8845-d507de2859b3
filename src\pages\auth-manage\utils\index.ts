import type { Scope } from '../api/ids4'
import type { MaybeRefOrGetter } from 'vue'

export function findScope(scopes: MaybeRefOrGetter<Scope[] | undefined>, name: string) {
  return computed(() => {
    const _scopes = toValue(scopes)
    if (!_scopes?.length) return
    for (const item of _scopes) {
      if (item.name === name) {
        return `${item.displayName} (${item.name})`
      }
    }
  })
}
