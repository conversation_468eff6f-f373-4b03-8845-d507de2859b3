<script lang="ts" setup>
import type { ThemeConfigKey } from '../composables/themeConfig'
import Ic<PERSON>harp<PERSON><PERSON> from '~icons/ic/sharp-person'
import { ElNotification } from 'element-plus'
import { useAuth } from 'vue-oidc-provider'

const { user, signoutRedirect } = useAuth()

function handleCommand(command: 'cleanCache' | 'signout') {
  if (command === 'signout') {
    signoutRedirect()
  } else if (command === 'cleanCache') {
    localStorage.clear()
    sessionStorage.clear()
    ElNotification({
      title: 'Success',
      message: '清除缓存成功',
      type: 'success',
      position: 'bottom-right',
    })
  }
}
function onTheme(key: ThemeConfigKey) {
  currentTheme.value = key
}
</script>

<template>
  <div
    class="flex items-center h-full text-.9rem px-30px color-white"
    style="background-color: var(--el-color-primary)"
  >
    <div flex-1 pr-20px>
      <Menus />
    </div>

    <div class="flex items-center color-white justify-end">
      <el-popover placement="top" title="主题" :width="300" trigger="hover">
        <div class="flex p10px">
          <div
            v-for="(item, key) in themeConfig"
            :title="item.text"
            :key="key"
            class="w-50px h-50px flex items-center justify-center"
          >
            <div
              class="color-white text-12px text-center lh-40px rounded-4px w-40px h-40px hover:w-45px hover:h-45px hover:lh-45px cursor-pointer transition-all"
              :style="{ backgroundColor: item.color }"
              @click="() => onTheme(key)"
            >
              {{ item.text }}
            </div>
          </div>
        </div>
        <template #reference>
          <div class="i-fluent:dark-theme-20-filled text-1.4rem mr-20px" />
        </template>
      </el-popover>

      <el-dropdown @command="handleCommand">
        <div class="flex items-center color-white">
          <el-avatar :icon="IcSharpPerson" :size="30" :src="user?.profile.Avatar" />
          <div class="m-l-10px">{{ user?.profile.UserName }}</div>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="cleanCache">清除缓存</el-dropdown-item>
            <el-dropdown-item command="signout" divided> 退出登录 </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
