import { fetchConfig, type FetchOptions } from '@duo-common/config-center'
import { presetOidcSettings } from '~/oidc/presetOidcSettings'
import { mergeWith } from 'lodash-es'
import type { UserManagerSettings } from 'oidc-client-ts'

export interface RemoteConfig {
  hosts: Record<HostName, string>
  sites: Record<SitesName, string>
  oidcSettings: UserManagerSettings
  aliId: string[]
}
enum HostName {
  auth = 'auth',
  ocSelector = 'ocSelector',
  gateway = 'gateway',
  bpm = 'bpm',
}

enum SitesName {
  studioSite = 'studioSite',
  adminSite = 'adminSite',
  bpmSite = 'bpmSite',
  formMobileSite = 'formMobileSite',
  formWebSite = 'formWebSite',
  messageCenterSite = 'messageCenterSite',
}

const remoteConfigDefine = {
  oidcSettings: presetOidcSettings,
} as RemoteConfig

export const loadRemoteConfig = async (options: FetchOptions): Promise<RemoteConfig> => {
  const CONFIG_SERVER = window.CONFIG_SERVER || location.origin + '/config'

  try {
    const remoteConfig = await fetchConfig(CONFIG_SERVER, options, {
      headers: {
        'Content-Type': 'application/json',
      },
    }).then((res) => res.json() as Promise<RemoteConfig>)
    return mergeWith(remoteConfigDefine, remoteConfig)
  } catch (error) {
    console.warn('load remote config error.')
  }
  return { ...remoteConfigDefine }
}

declare global {
  interface Window {
    CONFIG_SERVER: string
    remoteConfig: RemoteConfig
  }
  const remoteConfig: RemoteConfig
}
