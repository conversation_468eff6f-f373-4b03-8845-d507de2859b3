import {
  globalConfigProviderIconPrefix,
  globalConfigProviderTag,
  globalConfigProviderTheme,
  globalConfigProviderThemeDarkVars,
  globalConfigProviderThemeLightVars,
  globalConfigProviderThemeVars,
  globalConfigProviderThemeVarsScope,
  globalConfigProviderZIndex,
  type ConfigProviderPropsMaybeRefOrGetter,
} from './share'
import { MaybeRefOrGetterToRef } from '@unpages/utils'
import { ConfigProvider } from 'vant'
import { defineComponent, toValue } from 'vue'

interface UseVantOptions extends Partial<ConfigProviderPropsMaybeRefOrGetter> {}

export function useVant(options: UseVantOptions = {}) {
  const {
    tag = globalConfigProviderTag,
    theme = globalConfigProviderTheme,
    zIndex = globalConfigProviderZIndex,
    themeVars = globalConfigProviderThemeVars,
    themeVarsDark = globalConfigProviderThemeDarkVars,
    themeVarsLight = globalConfigProviderThemeLightVars,
    themeVarsScope = globalConfigProviderThemeVarsScope,
    iconPrefix = globalConfigProviderIconPrefix,
  } = options

  const _tag = MaybeRefOrGetterToRef(tag)
  const _theme = MaybeRefOrGetterToRef(theme)
  const _zIndex = MaybeRefOrGetterToRef(zIndex)
  const _themeVars = MaybeRefOrGetterToRef(themeVars)
  const _themeVarsDark = MaybeRefOrGetterToRef(themeVarsDark)
  const _themeVarsLight = MaybeRefOrGetterToRef(themeVarsLight)
  const _themeVarsScope = MaybeRefOrGetterToRef(themeVarsScope)
  const _iconPrefix = MaybeRefOrGetterToRef(iconPrefix)

  const VantConfigProvider = defineComponent({
    name: 'VantConfigProvider',
    setup(props, { slots }) {
      return () => (
        <ConfigProvider
          tag={toValue(_tag)}
          theme={toValue(_theme)}
          zIndex={toValue(_zIndex)}
          themeVars={toValue(_themeVars)}
          themeVarsDark={toValue(_themeVarsDark)}
          themeVarsLight={toValue(_themeVarsLight)}
          themeVarsScope={toValue(_themeVarsScope)}
          iconPrefix={toValue(_iconPrefix)}
        >
          {slots.default?.()}
        </ConfigProvider>
      )
    },
  })

  return {
    VantConfigProvider,
    tag: _tag,
    theme: _theme,
    zIndex: _zIndex,
    themeVars: _themeVars,
    themeVarsDark: _themeVarsDark,
    themeVarsLight: _themeVarsLight,
    themeVarsScope: _themeVarsScope,
    iconPrefix: _iconPrefix,
  } as const
}
