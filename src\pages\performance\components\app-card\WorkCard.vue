<script lang="ts" setup>
import type { M2 } from '~performance/api/performance'

const { performance } = useFetchApi()

const { data } = performance.statisticsData<'m2'>('m2')

function handleRowClick(row: M2) {
  navigateTo(appBaseURL.value + row.key, {
    open: {
      target: isMobileSize.value ? '_self' : '_blank',
    },
  })
}
</script>

<template>
  <Card title="办理情况" border>
    <el-table
      :data="data?.data"
      style="width: 100%"
      show-overflow-tooltip
      @row-click="handleRowClick"
    >
      <el-table-column prop="name" label="应用名称" />
      <el-table-column prop="value" label="办理次数" width="100" align="center" />
    </el-table>
  </Card>
</template>

<style lang="less" scoped>
@import '~performance/styles/table.less';
</style>
