<script lang="ts" setup>
import type { M7 } from '~performance/api/performance'

const router = useRouter()
const { performance } = useFetchApi()
const { data } = performance.statisticsData<'m7'>('m7')

function percentage(value: number): number {
  if (Number.isNaN(value) || !data.value?.data.length) return 0
  return (value / Number(data.value.data[0].value)) * 100
}
function handleRowClick(row: M7) {
  router.push({
    name: 'incident_view',
    query: {
      key: 'm7_m8',
      name: window.encodeURI('流程耗时排行'),
      startDate: performance.statisticsDate.value.startDate,
      endDate: performance.statisticsDate.value.endDate,
      processDefId: row.key,
    },
  })
}
</script>

<template>
  <Card title="流程耗时排行" style="--max-height-content: 460px">
    <el-table :data="data?.data" width="100%" show-overflow-tooltip @row-click="handleRowClick">
      <el-table-column prop="name" label="模版" width="120" />
      <el-table-column prop="value" label="耗时" width="100">
        <template #default="{ row }">
          {{ useFormatTime(Number(row.value)) }}
        </template>
      </el-table-column>
      <el-table-column>
        <template #default="{ row }">
          <el-progress
            :percentage="percentage(Number(row.value))"
            :stroke-width="14"
            :show-text="false"
          />
        </template>
      </el-table-column>
    </el-table>
  </Card>
</template>

<style lang="less" scoped></style>
