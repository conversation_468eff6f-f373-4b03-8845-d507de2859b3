<script setup lang="ts">
import { useAuth } from 'vue-oidc-provider'

const { isAuthenticated, isLoading, signinRedirect } = useAuth()

watch([isLoading, isAuthenticated], async ([isLoading, isAuthenticated]) => {
  if (!isLoading && !isAuthenticated) {
    signinRedirect()
  }
})
</script>

<template>
  <el-config-provider size="default">
    <el-container class="min-h-screen">
      <el-header style="--el-header-padding: 0; --el-header-height: 60px">
        <Header />
      </el-header>
      <el-container>
        <el-main class="bg-#f5f7fa" style="--el-main-padding: 0">
          <RouterView v-slot="{ Component }">
            <template v-if="Component">
              <KeepAlive>
                <Suspense>
                  <!-- 主要内容 -->
                  <component :is="Component"></component>

                  <!-- 加载中状态 -->
                  <template #fallback> 正在加载... </template>
                </Suspense>
              </KeepAlive>
            </template>
          </RouterView>
        </el-main>
      </el-container>
    </el-container>
  </el-config-provider>
</template>

<style scoped></style>
