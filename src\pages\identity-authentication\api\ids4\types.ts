export interface Client {
  id?: string
  icon: string
  clientId: string
  clientSecret: string
  clientName: string
  ownerIp: string
  ownerPort: string
  enabled: boolean
  allowedScopes: string[]
  allowedRoles: string
  redirectUris: string[]
  postLogoutRedirectUris: string[]
  ownerName: string
  ownerContactPerson: string
  ownerContactPhone: string
  serviceName: string
  serviceContactPerson: string
  serviceContactPhone: string
  returnUserProperties: string
  authProtocol: AuthProtocol
}

export enum AuthProtocol {
  OAuth = 0,
  Cas = 1,
}

export interface PageParams {
  searchText?: string
  page?: number
  pageSize?: number
}

export interface ApiResourcesParams extends PageParams {}

export interface ClientsParams extends PageParams {
  ownerName?: string
}

export interface ApiSourceModule {
  displayName: string
  scopes: Scope[]
}

export interface Scope {
  id: string
  name: string
  displayName: string
  description: string[]
}

export interface PasswordPolicy {
  requiredLength: number //密码长度
  requireDigit: boolean //必须包含数字
  requireUppercase: boolean //必须包含大写字母
  requireLowercase: boolean //必须包含小写字母
  requireNonAlphanumeric: boolean //必须包含特殊字符
}

export interface UserProperties {
  code: string
  name: string
}

export interface GrantLogsParams extends Page {
  clientId: string
  startTime: string
  endTime: string
}

export interface GrantLogs {
  id: string
  clientId: string
  clientName: string
  endPoint: string
  requestedScopes: string
  grantUserId: string
  grantUserName: string
  grantNickName: string
  returnUserProperties: string
  grantUserIp: string
  agentInfo: {
    requestUrl: string
    xOriginalFor: string
  }
  grantResult: APIResult<boolean>
  grantTime: string
}
