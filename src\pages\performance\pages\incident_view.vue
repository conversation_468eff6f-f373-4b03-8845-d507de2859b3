<script lang="ts" setup>
import type { StatisticsTasksBody, StatisticsTasksResult } from '~performance/api/engine'
import dayjs from 'dayjs'
import { ElMessageBox, ElNotification, type TableInstance } from 'element-plus'

enum KeyStatus {
  m1_v3 = 1, //办理中
  m1_v4 = 2, //已办结
  m1_v5 = 4, //已取消
  m1_v6 = 7, //停滞中
  // m7_m8 = 2, //已办结
  m10_m11 = 1, //办理中
  // m13_m14 = 2, //已办结
}
const InstanceStatus: { [key: number]: { text: string; color: string } } = {
  1: {
    text: '办理中',
    color: 'blue',
  },
  2: {
    text: '已办结',
    color: 'green',
  },
  4: {
    text: '已取消',
    color: 'red',
  },
  7: {
    text: '停滞中',
    color: 'gray',
  },
}

const route = useRoute()
const query = route.query as {
  key: string
  name: string
  startDate: string
  endDate: string
  processDefId?: string
  userId?: string
}
const { engine } = useFetchApi()
const { width } = useWindowSize()
const showSearch = ref(true)
watch(
  width,
  (_width) => {
    if (_width <= 1280) {
      showSearch.value = false
    } else {
      showSearch.value = true
    }
  },
  { immediate: true },
)
const page = usePageValue()
const params = ref<Partial<StatisticsTasksBody>>({
  bizKey: '',
  initiatorName: '',
  processName: '',
  summary: '',
  stepName: '',
  assigneeName: '',
})
const paramsDebounced = ref({ ...params.value })
watchDebounced(
  params,
  (_params) => {
    paramsDebounced.value = { ..._params }
  },
  { debounce: 500, deep: true },
)
const body = computed(
  () =>
    ({
      ...page,
      instanceStatus: ['m7_m8', 'm1_v2', 'm13_m14'].includes(query.key)
        ? []
        : [KeyStatus[query.key as any]],
      processDefIds: query.processDefId ? [query.processDefId] : undefined,
      assignee: query.userId,
      startDate: query.startDate,
      endDate: query.endDate,
      isDue: query.key === 'm13_m14' ? true : false,
      isActivate: query.key === 'm10_m11' ? true : false,
      ...paramsDebounced.value,
    }) as StatisticsTasksBody,
)
const { data, execute } = engine.getStatisticsTasks(body)
const [DefineTemplate, ReuseTemplate] = createReusableTemplate<{
  text?: string
}>()
const tableInstance = ref<TableInstance>()

function handleProcess(id: string) {
  navigateTo(processBaseURL.value + id, {
    open: {
      target: isMobileSize.value ? '_self' : '_blank',
    },
  })
}

function handleApprove(item: StatisticsTasksResult) {
  const uri = isMobileSize.value
    ? window.remoteConfig.sites.formMobileSite + '/processmap/' + item.instanceId
    : window.remoteConfig.sites.formWebSite +
      '/bpm/ProcessMonitor?process=' +
      item.processName +
      '&incident=' +
      item.instanceId
  navigateTo(uri, {
    open: {
      target: isMobileSize.value ? '_self' : '_blank',
    },
  })
}

function handleStopProcess() {
  const rows = tableInstance.value?.getSelectionRows() as StatisticsTasksResult[]
  if (!rows.length) {
    return ElNotification({
      type: 'warning',
      message: '请至少选择一条数据',
    })
  }
  const ids = rows.map((item) => item.instanceId).join(',')
  ElMessageBox.confirm('确认要停止选中流程吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const { data } = await engine.abortIncidentByInstanceId(ids)
    if (data.value?.code === 1 && data.value.data.successed) {
      ElNotification({
        type: 'success',
        message: data.value?.data?.message,
      })
      execute()
    } else {
      ElNotification({
        type: 'warning',
        message: data.value?.data?.message,
      })
    }
  })
}
</script>

<template>
  <DefineTemplate v-slot="{ $slots, text }">
    <div class="flex items-center">
      <div class="search-label mr-4px flex items-center">
        <div class="h-15px w-4px bg-#3464E0 mr-4px"></div>
        <Title size="14px" style="--title-color: #5c5c5c">{{ text }}</Title>
      </div>
      <div class="mx-4px flex-1">
        <component :is="$slots.default" />
      </div>
    </div>
  </DefineTemplate>

  <div class="max-sm:px-2 md:px-14 lg:px-20 xl:px-30 2xl:px-45">
    <Title size="20px">{{ decodeURI(query.name) }}</Title>

    <div class="search-btn mb-10px display-none">
      <el-button type="primary" link @click="() => (showSearch = !showSearch)">
        {{ showSearch ? '收起搜索' : '展开搜索' }}
      </el-button>
    </div>

    <div v-show="showSearch">
      <div class="search-container" flex my-8px>
        <ReuseTemplate text="业务单号">
          <el-input v-model="params.bizKey" placeholder="请输入业务单号" />
        </ReuseTemplate>
        <ReuseTemplate text="申请人">
          <el-input v-model="params.initiatorName" placeholder="请输入申请人" />
        </ReuseTemplate>
        <ReuseTemplate text="流程名称">
          <el-input v-model="params.processName" placeholder="请输入流程名称" />
        </ReuseTemplate>
        <ReuseTemplate text="摘要">
          <el-input v-model="params.summary" placeholder="请输入摘要" />
        </ReuseTemplate>
        <ReuseTemplate text="步骤名称">
          <el-input v-model="params.stepName" placeholder="请输入步骤名称" />
        </ReuseTemplate>
        <ReuseTemplate text="处理人名称">
          <el-input v-model="params.assigneeName" placeholder="请输入处理人名称" />
        </ReuseTemplate>
      </div>
    </div>

    <div class="p-10px bg-white rounded-10px">
      <div class="mb-8px">
        <el-button type="warning" plain @click="handleStopProcess">终止流程</el-button>
      </div>

      <el-table
        :data="data?.data"
        style="width: 100%"
        show-overflow-tooltip
        :row-style="{ height: '50px' }"
        ref="tableInstance"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="bizKey" label="业务单号" max-width="200" />
        <el-table-column prop="processName" label="流程" width="150">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleProcess(row.id)">{{
              row.processName
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="当前进度" width="100" />
        <el-table-column prop="instanceStatus" label="状态" width="80">
          <template #default="{ row }">
            <span class="text-12px" :style="{ color: InstanceStatus[row.instanceStatus].color }">
              {{ InstanceStatus[row.instanceStatus].text }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="initiatorName" label="申请人" width="100" />
        <el-table-column prop="applyDateTime" label="申请日期" width="150">
          <template #default="{ row }">
            {{ dayjs(row.applyDateTime).format('YYYY/MM/DD HH:mm') }}
          </template>
        </el-table-column>
        <el-table-column prop="summary" label="摘要" />
        <el-table-column label="操作" align="center" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleApprove(row)">查看审批进度</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        class="mt-20px"
        :page-sizes="[10, 15, 20, 25, 30, 40, 50]"
        layout="prev, pager, next, total, sizes"
        :total="data?.count"
        v-model:page-size="page.pageSize"
        v-model:current-page="page.pageIndex"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
@media (max-width: 1280px) {
  .search-label {
    width: 90px;
  }
  .search-btn {
    display: block;
  }
  .search-container {
    display: grid;
  }
}
</style>
