import { useStorage, type RemovableRef } from '@vueuse/core'

export type ThemeConfigKey = 'default' | 'green' | 'orange' | 'blue' | 'purple'

export type ThemeConfig = Record<
  ThemeConfigKey,
  {
    text: string
    color: string
  }
>

const themeConfig: ThemeConfig = {
  default: {
    text: '默认',
    color: '#222842',
  },
  green: {
    text: '绿色',
    color: '#00c87c',
  },
  orange: {
    text: '橘色',
    color: '#ff8647',
  },
  blue: {
    text: '蓝色',
    color: '#3464e0',
  },
  purple: {
    text: '紫色',
    color: '#6a69e8',
  },
}

export const currentTheme = useStorage<ThemeConfigKey>('Theme-Key', 'default')

watch(
  currentTheme,
  (value) => {
    document.body.className = `theme--${value}`
  },
  { immediate: true },
)

export default themeConfig
