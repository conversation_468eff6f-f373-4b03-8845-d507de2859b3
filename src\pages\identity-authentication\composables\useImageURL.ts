import { isArray } from 'lodash-es'

const formHost = remoteConfig.hosts.gateway + '/form'

export default function useImageURL(extra: string): string
export default function useImageURL(extras: string[]): string[]

export default function useImageURL(extra: string | string[]) {
  if (isArray(extra)) {
    return extra.map((item) => (item.includes('http') ? item : formHost + '/' + item))
  } else {
    return extra.includes('http') ? extra : formHost + '/' + extra
  }
}
