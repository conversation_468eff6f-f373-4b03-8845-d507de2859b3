import type { ClientCount, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lient<PERSON>ogParams, ClientRequestLog, ScopeCount } from './types'

export const baseURL = remoteConfig.hosts.gateway + '/auditLog'

const http = createService(baseURL)

enum API {
  CLIENT_LOG = '/api/LogEntry/getLogsByClient',
  CLIENT_REQUEST = '/api/LogEntry/get',
  SCOPE_COUNT = '/api/LogEntry/getCountByScope',
  CLIENT_COUNT = '/api/LogEntry/getCountByClient',
  PROJECT_COUNT = '/api/LogEntry/getCountByProject',
}

export function getClientLogs(body?: Partial<ClientLogParams>) {
  return http(API.CLIENT_LOG).post(body).json<APIResult<ClientLog[]>>()
}

export function getClientRequest(id: string) {
  return http(API.CLIENT_REQUEST + paramsSerializer({ id }))
    .post()
    .json<APIResult<ClientRequestLog>>()
}

export function getCountByScope(body = {}) {
  return http(API.SCOPE_COUNT).post(body).json<APIResult<ScopeCount[]>>()
}

export function getCountByClient(body = {}) {
  return http(API.CLIENT_COUNT).post(body).json<APIResult<ClientCount[]>>()
}

export function getCountByProject(body = {}) {
  return http(API.PROJECT_COUNT).post(body).json<APIResult<ScopeCount[]>>()
}
