<script lang="ts" setup>
import type { M14 } from '~performance/api/performance'

const router = useRouter()
const { performance } = useFetchApi()
const { data } = performance.statisticsData<'m14'>('m14')

function percentage(value: number): number {
  if (Number.isNaN(value) || !data.value?.data.length) return 0
  return (value / Number(data.value.data[0].value)) * 100
}
function handleRowClick(row: M14) {
  router.push({
    name: 'incident_view',
    query: {
      key: 'm13_m14',
      name: window.encodeURI('人员超时排行'),
      startDate: performance.statisticsDate.value.startDate,
      endDate: performance.statisticsDate.value.endDate,
      userId: row.key,
    },
  })
}
</script>

<template>
  <Card title="人员超时排行" style="--max-height-content: 460px">
    <el-table :data="data?.data" width="100%" show-overflow-tooltip @row-click="handleRowClick">
      <el-table-column prop="name" label="姓名" width="100" />
      <el-table-column prop="value" label="超时" width="100">
        <template #default="{ row }">
          {{ useFormatTime(Number(row.value)) }}
        </template>
      </el-table-column>
      <el-table-column>
        <template #default="{ row }">
          <el-progress
            :percentage="percentage(Number(row.value))"
            :stroke-width="14"
            :show-text="false"
          />
        </template>
      </el-table-column>
    </el-table>
  </Card>
</template>

<style lang="less" scoped></style>
