<script lang="ts" setup>
import type ClientForm from '../../components/ClientForm.vue'
import type { Client } from '~auth-manage/api/ids4'

const props = defineProps<{ id: string }>()

useTitle('更新应用')

const { ids4 } = useApi()
const { data, onFetchResponse } = ids4.getClientDetail(props.id)
const form = ref<Client>({
  icon: '',
  clientId: '',
  clientSecret: '',
  clientName: '',
  ownerIp: '',
  ownerPort: '',
  enabled: true,
  allowedScopes: [],
  redirectUris: [],
  postLogoutRedirectUris: [],
  ownerName: '',
  ownerContactPerson: '',
  ownerContactPhone: '',
  serviceName: '',
  serviceContactPerson: '',
  serviceContactPhone: '',
})
onFetchResponse(() => {
  form.value = data.value!
})
const formInstance = ref<InstanceType<typeof ClientForm>>()

async function submit() {
  formInstance.value
    ?.validate()
    .then(() => {
      ElMessageBox.confirm('确定修改吗', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const response = await ids4.updateClient(form.value)
        if (response.statusCode.value === 200) {
          createSuccess()
        } else {
          ElNotification({
            type: 'error',
            message: response.error.toString(),
          })
        }
      })
    })
    .catch((fields) => {
      console.log('error submit!', fields)
    })
}
function createSuccess() {
  window.dispatchEvent(createClientEvent)
  window.close()
}
</script>

<route lang="yaml">
name: 'update_client'
meta:
  hidden: true
</route>

<template>
  <MainTemplate>
    <h2 class="mt-10px mb-20px color-gray-7">更新应用</h2>
    <ClientForm v-model="form" ref="formInstance" />

    <div class="text-center mt-10px">
      <el-button type="primary" size="large" @click="submit">确认修改</el-button>
    </div>
  </MainTemplate>
</template>

<style lang="less" scoped></style>
