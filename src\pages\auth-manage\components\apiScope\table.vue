<script lang="ts" setup>
import type { Scope } from '~auth-manage/api/ids4'
import type { TableInstance } from 'element-plus'

const props = defineProps<{ modelValue: string[] }>()
const emit = defineEmits(['update:modelValue'])
const modelValue = useVModel(props, 'modelValue', emit)

const { ids4 } = useApi()
const current = ref<string>('')
const tableInstance = ref<TableInstance>()
const { data, onFetchResponse } = ids4.apiSourceModules()
const modules = ref<Scope[]>([])

const syncModules = (name?: string) => {
  if (!name) return
  current.value = name
  modules.value = data.value?.find((item) => item.displayName === name)?.scopes || []
  nextTick(() => {
    modules.value?.forEach((item) => {
      if (modelValue.value.includes(item.name)) {
        tableInstance.value?.toggleRowSelection(item, true)
      }
    })
  })
}

onFetchResponse(async () => {
  // if (data.value) {
  //   data.value = data.value?.filter((item) => item.displayName !== '消息中心')
  // }
  syncModules(data.value?.[0].displayName)
})
function handleSelect(row: Scope) {
  const index = modelValue.value.findIndex((item) => item === row.name)
  if (index === -1) {
    modelValue.value.push(row.name)
  } else {
    modelValue.value.splice(index, 1)
  }
}
function handleSelectAll(selection: Scope[]) {
  ;[...(selection.length ? selection : modules.value)].forEach((row) => {
    handleSelect(row)
  })
}
</script>

<template>
  <el-container>
    <el-aside width="150px">
      <el-menu
        :default-active="current"
        class="h-full"
        style="--el-color-primary-dark-2: white; --el-menu-hover-bg-color: white"
      >
        <el-menu-item
          v-for="item in data"
          :key="item.displayName"
          :index="item.displayName"
          @click="() => syncModules(item.displayName)"
        >
          {{ item.displayName || '空' }}
        </el-menu-item>
      </el-menu>
    </el-aside>

    <el-main style="--el-main-padding: 0 20px">
      <el-table
        ref="tableInstance"
        :data="modules || []"
        style="width: 100%"
        :row-style="{ height: '60px' }"
        show-overflow-tooltip
        @select="(selection, row) => handleSelect(row)"
        @select-all="handleSelectAll"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="code" />
        <el-table-column prop="displayName" label="权限信息" />
        <el-table-column prop="description" label="接口">
          <template #default="{ row }">
            <div v-for="(item, i) in row.description" :key="i" class="my-6px flex items-center">
              <div i-material-symbols:attach-file-rounded class="mr-2px"></div>
              <span v-html="item"></span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-main>
  </el-container>
</template>

<style scoped>
.theme--default .el-menu {
  --active-bg-color: #008bfb;
  --el-color-primary: #008bfb;
  --el-tabs-header-height: 56px;
}
:deep(.api_description) {
  color: #008bfb;
  text-decoration: underline;
}
</style>
