import { ElResult } from "element-plus";
import "element-plus/es/components/result/style/css";

export const CallbackRepl = defineComponent({
  components: { ElResult },
  setup() {
    return () => (
      <div class="h-100vh">
        <el-result icon="success" title="登录成功" sub-title="即将进入系统" class="h-100%"></el-result>
      </div>
    );
  },
});

export const PopupCallbackRepl = defineComponent({
  setup() {
    return () => <span>PopupCallbackRepl</span>;
  },
});

export const SilentRenewCallbackRepl = defineComponent({
  setup() {
    return () => <span>SilentRenewCallbackRepl</span>;
  },
});
