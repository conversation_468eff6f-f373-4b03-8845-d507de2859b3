{"name": "unpages", "version": "0.0.0", "private": true, "packageManager": "pnpm@9.15.1", "engines": {"node": ">=20.0.0"}, "scripts": {"dev": "tsx scripts/dev.ts", "build": "tsx scripts/build.ts", "build:all": "tsx scripts/build.ts --all", "example": "tsx scripts/example.ts", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write ."}, "dependencies": {"@duo-common/core": "0.0.18", "@duo-common/icon-v3": "0.0.20", "@iconify/vue": "4.1.1", "@unpages/components": "workspace:*", "@vueuse/core": "10.9.0", "core-js": "3.34.0", "dayjs": "1.11.10", "echarts": "5.4.3", "element-plus": "2.7.0", "oidc-client-ts": "2.4.0", "pinia": "2.1.7", "vant": "4.7.3", "vconsole": "3.15.1", "vue": "3.4.21", "vue-oidc-provider": "1.0.0", "vue-router": "4.2.5", "vue3-oidc": "0.1.15", "xlsx": "^0.18.5"}, "devDependencies": {"@duo-common/config-center": "0.0.8", "@duo-common/project-info-plugin": "0.0.12", "@ianvs/prettier-plugin-sort-imports": "4.1.1", "@iconify/json": "2.2.159", "@rushstack/eslint-patch": "1.5.1", "@tsconfig/node20": "20.1.2", "@types/echarts": "4.9.22", "@types/lodash-es": "4.17.12", "@types/node": "20.9.0", "@types/prompts": "2.4.9", "@unpages/core": "workspace:*", "@unpages/init": "workspace:*", "@unpages/utils": "workspace:*", "@vant/auto-import-resolver": "1.0.2", "@vitejs/plugin-legacy": "5.2.0", "@vitejs/plugin-vue": "4.4.0", "@vitejs/plugin-vue-jsx": "3.0.2", "@vue/eslint-config-prettier": "8.0.0", "@vue/eslint-config-typescript": "12.0.0", "@vue/tsconfig": "0.4.0", "eslint": "8.53.0", "eslint-plugin-vue": "9.18.1", "fast-glob": "3.3.2", "jiti": "1.21.0", "less": "4.2.0", "lodash-es": "4.17.21", "npm-run-all2": "6.1.1", "prettier": "3.0.3", "prompts": "2.4.2", "terser": "5.26.0", "tsx": "4.6.1", "typescript": "5.2.2", "unocss": "0.58.0", "unplugin-auto-import": "0.17.2", "unplugin-element-plus": "0.8.0", "unplugin-icons": "0.18.1", "unplugin-vue-components": "0.25.2", "vite": "5.2.7", "vite-plugin-inspect": "0.8.1", "vite-plugin-pages": "0.32.0", "vue-tsc": "1.8.22"}}