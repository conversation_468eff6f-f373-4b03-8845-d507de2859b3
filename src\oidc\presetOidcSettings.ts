import { WebStorageStateStore, type UserManagerSettings } from 'oidc-client-ts'

export const presetOidcSettings: UserManagerSettings = {
  scope: 'email profile roles openid iduo.api offline_access',
  client_secret: 'INTERNAL-b5d5-7eba-1d182998574a',
  authority: 'https://dev.iduo.cc:4500',
  client_id: 'INTERNAL00000000CODE',
  redirect_uri: location.origin + '/oidc-callback',
  popup_redirect_uri: location.origin + '/oidc-popup-callback',
  silent_redirect_uri: location.origin + '/silent-renew-oidc.html',
  loadUserInfo: true,
  userStore: new WebStorageStateStore({ store: window.localStorage }),
  automaticSilentRenew: true,
} satisfies UserManagerSettings
