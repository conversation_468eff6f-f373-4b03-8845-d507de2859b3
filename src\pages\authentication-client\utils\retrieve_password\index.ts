import type { UserIdentity } from '..'
import type { PasswordPolicy } from '../../api/ids4/types'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { createFetch, type RemovableRef } from '@vueuse/core'
import type { User } from 'oidc-client-ts'

const AUTH_URL = '/connect/token'

const http = createFetch({
  baseUrl: remoteConfig.oidcSettings.authority,
  options: {
    timeout: 30 * 1000,
  },
  fetchOptions: {
    headers: {
      Accept: 'application/json, text/plain, */*',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  },
})

const retrievePasswordState = useStorage<User | null>(
  'retrieve_password:user',
  null,
  localStorage,
  {
    serializer: {
      read: (v) => (v ? JSON.parse(v) : null),
      write: (v) => JSON.stringify(v),
    },
  },
)

export async function getTokenByPhoneCode(params: UserIdentity) {
  const formData = new FormData()
  for (const key in remoteConfig.phoneCodeOption) {
    formData.append(key, remoteConfig.phoneCodeOption[key])
  }
  formData.append('phone_number', params.phone)
  formData.append('code', params.code)
  const result = await http(AUTH_URL)
    .post(formDataToString(formData))
    .json<User | { error: string }>()
  if (!('error' in result.data.value!)) {
    retrievePasswordState.value = result.data.value!
    retrievePasswordState.value.expires_at = Date.now() + result.data.value?.expires_in! * 1000
  }
  return result
}

export function formDataToString(formData: FormData) {
  return Array.from(formData.entries())
    .map((pair) => encodeURIComponent(pair[0]) + '=' + encodeURIComponent(pair[1].toString()))
    .join('&')
}

export function checkRetrievePasswordToken(): false | string {
  if (!retrievePasswordState.value || !retrievePasswordState.value.access_token) return false
  if (!retrievePasswordState.value.expires_at) return false
  // token 过期
  if (Date.now() > retrievePasswordState.value.expires_at!) return false
  return retrievePasswordState.value.access_token
}

export function clearRetrievePasswordToken() {
  retrievePasswordState.value = null
}

export function isPasswordValid(password: string, rule: PasswordPolicy) {
  const reString = useRegExp()
    .add('^')
    .add(rule.requireLowercase ? '(?=.*[a-z])' : '') // 至少一个小写字母
    .add(rule.requireUppercase ? '(?=.*[A-Z])' : '') // 至少一个大写字母
    .add(rule.requireDigit ? '(?=.*\\d)' : '') // 至少一个数字
    .add(rule.requireNonAlphanumeric ? '(?=.*[!@#$%^&*()\\-_=+{};:,<.>])' : '') // 至少一个特殊字符
    .add(rule.requiredLength ? `.{${rule.requiredLength},}` : '') // 至少 8 个字符
    .add('$')
    .toString()

  const re = new RegExp(reString)

  return {
    valid: re.test(password),
    message: `密码必须包含
    ${rule.requireUppercase ? '大写字母、' : ''}
    ${rule.requireLowercase ? '小写字母、' : ''}
    ${rule.requireDigit ? '数字、' : ''}
    ${rule.requireNonAlphanumeric ? '特殊字符' : ''}
    ${rule.requiredLength ? `和 ${rule.requiredLength} 个字符` : ''}
    `,
  }
}

export function useRegExp() {
  let result = ''
  function add(value: string | ((context: string) => string)) {
    result += typeof value === 'function' ? value(result) : value
    return shell
  }
  function toString() {
    return result
  }
  function toRegExp() {
    return new RegExp(result)
  }
  const shell = {
    add,
    toString,
    toRegExp,
  }
  return shell
}

export { retrievePasswordState }
