<script lang="ts" setup>
import { AppIcon, IconPreview, IconSelect } from '@duo-common/icon-v3'
import type { ButtonProps, PopoverProps } from 'element-plus'

const props = withDefaults(
  defineProps<{
    modelValue: string
    buttonProps?: Partial<ButtonProps>
    popoverProps?: Partial<PopoverProps>
    type?: '' | 'appIcon' | 'default'
  }>(),
  {
    type: '',
    buttonProps: () => ({
      type: 'primary',
    }),
    popoverProps: () => ({
      width: 700,
      trigger: 'hover',
      title: '选择图标',
      placement: 'bottom-start',
      popperStyle: { height: '500px', overflowY: 'auto' },
    }),
  },
)
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()
const vModel = useVModel(props, 'modelValue', emit)
</script>

<template>
  <el-popover v-bind="popoverProps">
    <AppIcon v-model="vModel" v-if="type === 'appIcon'"></AppIcon>
    <IconSelect v-model="vModel" v-else></IconSelect>
    <template #reference>
      <el-button v-bind="buttonProps">
        <IconPreview v-if="vModel" :name="vModel" />
        <el-icon size="20">
          <i class="i-ic-outline-arrow-drop-down"></i>
        </el-icon>
      </el-button>
    </template>
  </el-popover>
</template>

<style lang="less" scoped></style>
