import { replacePlaceholder } from '@duo-common/core'
import { cloneDeepWith } from 'lodash-es'

export enum OpenTypeEnum {
  视图容器 = 1,
  框架中 = 2,
  新窗口 = 3,
  当前窗口 = 4,
  自定义 = 5,
}

export type DateType = string | number | Date

export interface MenuBody {
  id: string
  url: string
  name: string
  moduleCode: string
  parentId: string | null
  iconName: string
  isDisabled: boolean
  isSelected: boolean
  isSetTop: boolean
  openType: OpenTypeEnum
  createdTime: DateType
  updatedTime: DateType
  ext1: string | null
  ext2: string | null
  ext3: string | null
  ext4: string | null
  ext5: string | null
  subMenus: MenuBody[]
}

export interface MenuConfig {
  menus: MenuBody[]
}

export const menuConfig = ref<MenuConfig>({ menus: [] })

export type MaybePromise<T, A = unknown> = (args?: A) => T | ((args?: A) => Promise<T>)

export async function initMenus(menus: MenuBody[] | MaybePromise<MenuBody[]>) {
  let _menus: MenuBody[] = []
  if (menuConfig.value.menus.length > 0) return
  if (typeof menus === 'function') {
    _menus = (await menus()) as MenuBody[]
  } else {
    _menus = menus
  }
  menuConfig.value.menus = transformMenus(_menus)
}

export function transformMenus(menus: MenuBody[]): MenuBody[] {
  const _routes: MenuBody[] = []
  for (const menu of menus) {
    menu.url = replacePlaceholder(
      menu.url,
      { ...remoteConfig.sites, ...remoteConfig.hosts },
      {
        placeholder: ['{{', '}}'],
      },
    )
    if (menu.subMenus?.length) {
      menu.subMenus = transformMenus(cloneDeepWith(menu.subMenus))
    }
    _routes.push(menu)
  }
  return _routes
}

export function isAuthMenu(
  path: string,
  routes: MenuBody[] = menuConfig.value.menus,
): [boolean, MenuBody | null] {
  const result = ref<[boolean, MenuBody | null]>([false, null])

  for (const route of routes) {
    if (route?.url === path) {
      result.value = [true, route]
    }
    if (route.subMenus?.length) {
      const value = isAuthMenu(path, route.subMenus)
      if (value[0]) result.value = value
    }
  }

  return result.value
}
