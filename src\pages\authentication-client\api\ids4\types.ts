import type { DateLike } from '@vueuse/core'

export interface User {
  userName: string
  nickName: string
  userNameSpell: string
  email: string
  lang: unknown | null
  mobileNo: string
  id: string
  adName: string
  createdTime: DateLike
  lastUpDateTime: DateLike | null
  lastUpDateUserId: DateLike | null
  isDisabled: boolean
  isAsync: boolean
  orderNo: number
}

export interface ChangePassword {
  newPassword: string
  confirmPassword: string
}

export interface ResetPassword {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

export enum VerificationCodeType {
  SIGNUP,
  CHANGE_PASSWORD,
}

export interface SmsCode {
  mobile: string
  verificationCodeType: VerificationCodeType
}

export interface PasswordPolicy {
  requiredLength: number //密码长度
  requireDigit: boolean //必须包含数字
  requireUppercase: boolean //必须包含大写字母
  requireLowercase: boolean //必须包含小写字母
  requireNonAlphanumeric: boolean //必须包含特殊字符
}
