<script lang="ts" setup>
import { unTransformClient, type TransformClient } from '../../logic'
import { useResetRef } from '@unpages/utils'
import type ClientForm from '~identity-authentication/components/ClientForm.vue'

useTitle('新建应用')

const [form, reset] = useResetRef<TransformClient>({
  icon: '',
  clientId: '',
  clientSecret: '',
  clientName: '',
  ownerIp: '',
  ownerPort: '',
  enabled: true,
  allowedRoles: [],
  allowedScopes: [],
  redirectUris: [],
  postLogoutRedirectUris: [],
  ownerName: '',
  ownerContactPerson: '',
  ownerContactPhone: '',
  serviceName: '',
  serviceContactPerson: '',
  serviceContactPhone: '',
  returnUserProperties: [],
  authProtocol: 0,
})
const { ids4 } = useApi()
const formInstance = ref<InstanceType<typeof ClientForm>>()
function submit() {
  formInstance.value
    ?.validate()
    .then(async () => {
      const response = await ids4.createClient(unTransformClient(form.value))
      if (response.statusCode.value === 201) {
        createSuccess()
      } else {
        ElNotification({
          type: 'error',
          message: response.error.toString(),
        })
      }
    })
    .catch((fields) => {
      console.log('error submit!', fields)
    })
}
function resetForm() {
  reset()
  formInstance.value?.clearValidate()
}

function createSuccess() {
  window.dispatchEvent(createClientEvent)
  window.close()
}
</script>

<route lang="yaml">
name: client
meta:
  hidden: true
</route>

<template>
  <MainTemplate>
    <h2 class="mt-10px mb-20px color-gray-7">新建应用</h2>
    <ClientForm v-model="form" ref="formInstance" />

    <div class="text-center mt-10px">
      <el-button size="large" @click="resetForm">重置</el-button>
      <el-button type="primary" size="large" @click="submit">立即创建</el-button>
    </div>
  </MainTemplate>
</template>

<style lang="less" scoped></style>
