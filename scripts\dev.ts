import { resolve } from 'node:path'
import {
  ChoiceValue,
  inlineRootUnPageConfig,
  inlineUnPageConfig,
  mergeViteUnPageConfig,
  parseUnPages,
  UnPageConfig,
  ViteUnPageConfig,
} from '@unpages/core'
import prompts from 'prompts'
import { createServer, mergeConfig, UserConfig } from 'vite'
import { pathToFileURL } from 'node:url'

const cwd = process.cwd()

  ; (async () => {
    const rootUnpageConfig = mergeConfig(
      inlineRootUnPageConfig,
      (await import(resolve(cwd, './unpage.config.ts'))).default,
    ) as Required<UnPageConfig>

    const answer = process.argv[2]

    const choices = await parseUnPages(answer)

    if (!choices.length) {
      console.log("No find pages, Please add in 'src/pages' path")
      process.exit(1)
    }

    let unpages: ChoiceValue = choices[0].value

    if (!answer) {
      ; ({ unpages } = (await prompts({
        name: 'unpages',
        type: 'select',
        message: 'Choose the agent',
        choices,
      })) as { unpages: ChoiceValue })
    }

    if (!unpages) {
      return
    }

    const { default: globalViteConfig } = await import(cwd + '/vite.config.ts')
    //merge global vite.config and user vite.config
    const viteConfig: UserConfig = mergeConfig(
      globalViteConfig,
      (await import(unpages.path + '/vite.config.ts')).default,
    )
    //merge user unpage.config and default value
    const unpageConfig: UnPageConfig = mergeConfig(
      inlineUnPageConfig,
      (await import(unpages.path + '/unpage.config.ts')).default,
    )
    //merge viteConfig and unPageConfig
    const viteUnPageConfig: ViteUnPageConfig = mergeConfig(viteConfig, unpageConfig)

    const config = mergeViteUnPageConfig(rootUnpageConfig, viteUnPageConfig, unpages)

    const server = await createServer(config)
    await server.listen()

    server.printUrls()
  })()
