//https://github.com/hannoeru/vite-plugin-pages
import { useUser } from '@/oidc'
import { isAuthMenu, menuConfig } from '@unpages/init'
import { initNavigate } from '@unpages/utils'
import routes from '~pages'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({ routes: routes, history: createWebHistory(import.meta.env.BASE_URL) })
console.log('vite-plugin-pages is routes: ', router.getRoutes())

initNavigate(router)

const user = useUser()

router.beforeEach((to, from, next) => {
  if (to.meta.public || to.meta.hidden || !user?.access_token) return next()
  const [pass] = isAuthMenu(to.path)
  if (pass) {
    return next()
  }
  return next('/404')
})

export default router
