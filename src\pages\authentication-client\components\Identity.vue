<script lang="ts" setup>
import { getTokenByPhoneCode, type UserBasic, type UserIdentity } from '../utils'
import { useResetRef } from '@unpages/utils'
import { VerificationCodeType } from '~authentication-client/api/ids4/types'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { User } from 'oidc-client-ts'

const props = defineProps<{ userBasic?: UserBasic }>()

const { ids4 } = useApi()
const formRef = ref<FormInstance>()
const form = reactive<UserIdentity>({
  phone: '',
  code: '',
})
const [counter, resetCounter] = useResetRef(60)
const rules = reactive<FormRules<UserIdentity>>({
  phone: [
    { required: true, message: '手机号不能为空', trigger: 'blur' },
    { type: 'string', message: '请填写正确的手机号', pattern: /^1[3-9]\d{9}$/, trigger: 'blur' },
    {
      asyncValidator: async (rule, value, callback) => {
        if (!props.userBasic?.userName) {
          return callback(new Error('用户名不能为空，请返回上一步'))
        }
        const { data } = await ids4.validateUserPhone({
          userName: props.userBasic.userName,
          phone: value,
        })
        if (data.value?.data === false) {
          callback(new Error('手机号不正确，与账号不匹配'))
        }
        callback()
      },
      trigger: 'blur',
    },
  ],
  code: { required: true, message: '验证码不能为空', trigger: 'blur' },
})
const { pause, resume, isActive } = useIntervalFn(
  () => {
    if (counter.value === 0) {
      pause()
      resetCounter()
      return
    }
    counter.value--
  },
  1000,
  { immediate: false },
)

function sendCode() {
  formRef.value?.validateField('phone', async (valid) => {
    if (valid) {
      try {
        const { data } = await ids4.smsCode({
          mobile: form.phone,
          verificationCodeType: VerificationCodeType.CHANGE_PASSWORD,
        })
        if (data.value?.data === true) {
          resume()
        } else {
          ElMessage.error(data.value?.message)
        }
      } catch (err) {
        console.log(err)
      }
    }
  })
}

function validate(): Promise<User | null> {
  return new Promise((resolve, reject) => {
    formRef.value?.validate(async (valid) => {
      if (valid) {
        const { data } = await getTokenByPhoneCode(form)
        if ('error' in data.value!) {
          reject(data.value.error)
        } else {
          resolve(data.value)
        }
      } else {
        reject('表单验证不通过')
      }
    })
  })
}

function clearForm() {
  form.phone = ''
  form.code = ''
  formRef.value?.clearValidate()
}

defineExpose({
  validate,
  clearForm,
})
</script>

<template>
  <el-form :model="form" :rules="rules" ref="formRef" label-position="top" label-width="auto">
    <el-form-item label="手机号" prop="phone">
      <el-input v-model="form.phone" placeholder="请输入手机号" />
    </el-form-item>
    <el-form-item label="验证码" prop="code">
      <el-input
        v-model="form.code"
        oninput="value=value.replace(/[^\d]/g,'')"
        placeholder="请输入验证码"
      >
        <template #append>
          <el-button @click="sendCode" v-if="!isActive">发送验证码</el-button>
          <span class="w-70px text-center" v-else>{{ counter }}s</span>
        </template>
      </el-input>
    </el-form-item>
  </el-form>
</template>

<style scoped></style>
