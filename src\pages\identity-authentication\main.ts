import { loadRemoteConfig } from '~/config-center'

const scriptEL = document.createElement('script')
scriptEL.src = __APP_BASE__ + '/config.js' + '?t=' + Date.now()
scriptEL.onload = () => {
  loadRemoteConfig({
    department: 'web',
    project: 'iduo.identity.authentication', //填入自己项目的配置名称
    version: '1',
  }).then(async (config) => {
    window.remoteConfig = config
    import('./bootstrap')
  })
}

document.body.appendChild(scriptEL)
