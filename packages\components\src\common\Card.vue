<script lang="ts" setup>
defineProps<{
  title?: string;
  rightText?: string;
  border?: boolean;
}>();
defineSlots<{
  default(): any;
  title(): any;
  "right-text"(): any;
}>();
defineEmits<{
  rightClick: [void];
}>();
</script>

<template>
  <div class="app-card" style="background: var(--bg-color)">
    <!-- header -->
    <div class="flex items-center" style="margin-bottom: var(--header-margin-bottom)">
      <div class="flex items-center flex-1">
        <div class="h-15px w-4px bg-#3464E0 mr-8px"></div>
        <div class="text-15px">
          <slot name="title">{{ title }}</slot>
        </div>
      </div>
      <el-button type="primary" link>
        <slot name="right-text">{{ rightText }}</slot>
      </el-button>
    </div>
    <!-- main -->
    <div
      class="app-card__content overflow-scroll"
      :class="[border && 'app-card__content__border']"
      style="max-height: var(--max-height-content)"
    >
      <slot name="default"></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
.app-card {
  --bg-color: white;
  --header-margin-bottom: 20px;
  --max-height-content: 240px;
}
.app-card__content__border {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
}
.app-card__content::-webkit-scrollbar {
  width: 4px;
}

.app-card__content::-webkit-scrollbar-thumb {
  background: #ccc; // 滑块颜色
  border-radius: 4px; // 滑块圆角
}

.app-card__content::-webkit-scrollbar-thumb:hover {
  background: var(--el-color-primary-light-9);
}
</style>
