<script setup lang="ts">
import en from 'element-plus/dist/locale/en.mjs'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

const language = ref('zh-cn')
const locale = computed(() => (language.value === 'zh-cn' ? zhCn : en))
</script>

<template>
  <el-config-provider :locale="locale">
    <el-container style="height: 100vh">
      <el-header>Header</el-header>
      <el-container>
        <el-aside width="200px">Aside</el-aside>
        <el-main>Main</el-main>
      </el-container>
    </el-container>
  </el-config-provider>
</template>

<style>
body {
  margin: 0;
}
.el-header {
  background: #bcdffe;
}
.el-aside {
  background: #d2eafe;
}
.el-main {
  background: #e8f4ff;
}
</style>
