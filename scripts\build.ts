import { resolve } from 'node:path'
import {
  ChoiceValue,
  getLastPath,
  inlineRootUnPageConfig,
  inlineUnPageConfig,
  mergeViteUnPageConfig,
  parseUnPages,
  type UnPageConfig,
  type ViteUnPageConfig,
} from '@unpages/core'
import prompts from 'prompts'
import { build, mergeConfig, type UserConfig } from 'vite'

const cwd = process.cwd()
;(async () => {
  const rootUnpageConfig = mergeConfig(
    inlineRootUnPageConfig,
    (await import(resolve(cwd, './unpage.config.ts'))).default,
  ) as Required<UnPageConfig>

  if (rootUnpageConfig.notBuild) return

  const answer = process.argv[2]

  const choices = await parseUnPages(answer === '--all' ? '' : answer)

  if (!choices.length) {
    console.log("No find pages, Please add in 'src/pages' path")
    process.exit(1)
  }

  let unpages: ChoiceValue[] = choices[0].value

  if (answer === '--all') {
    unpages = choices.map((item) => item.value)
  } else if (answer) {
    unpages = choices.filter((item) => getLastPath(item.title) === answer).map((item) => item.value)
  } else {
    const result = await prompts({
      name: 'unpages',
      type: 'select',
      message: 'Choose the agent',
      choices,
    })
    unpages = [result.unpages] as ChoiceValue[]
  }

  if (!unpages.length) {
    console.log("No find pages, Please add in 'src/pages' path")
    process.exit(1)
  }

  const { default: globalViteConfig } = await import(cwd + '/vite.config.ts')
  await Promise.all(
    unpages.map(async (entry) => {
      //merge global vite.config and user vite.config
      const viteConfig: UserConfig = mergeConfig(
        globalViteConfig,
        (await import(entry.path + '/vite.config.ts')).default,
      )
      //merge user unpage.config and default value
      const unpageConfig: UnPageConfig = mergeConfig(
        inlineUnPageConfig,
        (await import(entry.path + '/unpage.config.ts')).default,
      )
      //merge viteConfig and unPageConfig
      const viteUnPageConfig: ViteUnPageConfig = mergeConfig(viteConfig, unpageConfig)

      if (viteUnPageConfig.notBuild === true) {
        console.info(`${entry.path}: is Not Build`)
        return
      }

      const config = mergeViteUnPageConfig(rootUnpageConfig, viteUnPageConfig, entry, 'production')

      return build(config)
    }),
  )
  console.log('build down')
  process.exit(0)
})()
