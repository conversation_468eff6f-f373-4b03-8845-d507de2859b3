export interface Client {
  id?: string
  icon: string
  clientId: string
  clientSecret: string
  clientName: string
  ownerIp: string
  ownerPort: string
  enabled: boolean
  allowedScopes: string[]
  redirectUris: string[]
  postLogoutRedirectUris: string[]
  ownerName: string
  ownerContactPerson: string
  ownerContactPhone: string
  serviceName: string
  serviceContactPerson: string
  serviceContactPhone: string
}

export interface PageParams {
  searchText?: string
  page?: number
  pageSize?: number
}

export interface ApiResourcesParams extends PageParams {}

export interface ClientsParams extends PageParams {
  ownerName?: string
}

export interface ApiSourceModule {
  displayName: string
  scopes: Scope[]
}

export interface Scope {
  id: string
  name: string
  displayName: string
  description: string[]
}
