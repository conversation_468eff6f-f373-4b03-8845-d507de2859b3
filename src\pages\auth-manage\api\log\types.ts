export interface ClientLogParams {
  pageIndex?: number
  pageSize?: number
  keyWord?: string
  sortField?: string
  sortType?: string
  totalCount?: number
  eventName?: string
  userIdentifier?: string
  userName?: string
  actionType?: string
  actionTimeStart?: Date | string | number | ''
  actionTimeEnd?: Date | string | number | ''
  project?: string
  requestUrl?: string
  referer?: string
  clientIp?: string
}

export interface ClientLog {
  id: string
  eventName: string //接口名称
  userIdentifier: string
  userName: string //应用名称
  actionType: string
  actionTime: Date | string | number | '' //调用时间
  project: string
  requestUrl: string //接口地址
  referer: string
  clientIp: string //请求IP
  duration: number
  type: number
  returnResultCode: number
}

export interface ClientRequestLog {
  id: string
  clientBrowserInfo?: string
  actionData?: string
}

export interface ScopeCount {
  key: string
  name: string
  total: number
  successCount: number
  errorCount: number
}

export interface ClientCount extends ScopeCount {}
