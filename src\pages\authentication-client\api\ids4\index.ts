import type { User<PERSON>asic, UserPhone } from '../../utils'
import type { ChangePassword, PasswordPolicy, ResetPassword, SmsCode, User } from './types'

export const baseURL = remoteConfig.hosts.gateway + '/ids4api'
const noAuthBaseURL = remoteConfig.hosts.gateway + '/noauth/ids4api'

const http = createService(baseURL, {
  options: {
    afterFetch(ctx) {
      if (!ctx.response.status.toString().startsWith('2')) {
        ElNotification({
          type: 'error',
          title: '请求 Error',
          message: ctx.data.message ? ctx.data.message : 'API请求错误,请检查网络',
        })
      }
      return ctx
    },
  },
})

const noAuthHttp = createService(noAuthBaseURL, {
  options: {
    beforeFetch(ctx) {
      return ctx
    },
    afterFetch(ctx) {
      if (!ctx.response.status.toString().startsWith('2')) {
        ElNotification({
          type: 'error',
          title: '请求 Error',
          message: ctx.data.message ? ctx.data.message : 'API请求错误,请检查网络',
        })
      }
      return ctx
    },
  },
})

enum API {
  GET_USER_BY_USERNAME = '/api/NewOchart/GetUserByUserName',
  CHANGE_PASSWORD = '/api/NewOchart/ChangePassword',
  RESET_PASSWORD = '/api/NewOchart/ResetPassword',
  SMS_CODE = '/api/NewOchart/SmsCode',
  VALIDATE_USERNAME = '/api/NewOchart/ValidateUserName',
  VALIDATE_USER_PHONE = '/api/NewOchart/ValidateUserPhone',
  IDENTITY_PASSWORD = '/api/Users/<USER>/Password',
}

export function getUserByUsername(username: string) {
  return http(API.GET_USER_BY_USERNAME + paramsSerializer({ username }))
    .get()
    .json<APIResult<User>>()
}

export function changePassword(body: ChangePassword, token: string) {
  return http(API.CHANGE_PASSWORD, {
    beforeFetch({ options }) {
      options.headers = {
        ...options.headers,
        Authorization: `Bearer ${token}`,
      }
    },
  })
    .post(body)
    .json<APIResult<boolean>>()
}

export function resetPassword(body: ResetPassword) {
  return http(API.RESET_PASSWORD).post(body).json<APIResult<boolean>>()
}

export function smsCode(body: SmsCode) {
  return noAuthHttp(API.SMS_CODE + paramsSerializer(body))
    .post()
    .json<APIResult<boolean>>()
}

export function validateUsername(params: UserBasic) {
  return noAuthHttp(API.VALIDATE_USERNAME + paramsSerializer(params))
    .get()
    .json<APIResult<boolean>>()
}

export function validateUserPhone(params: UserPhone) {
  return noAuthHttp(API.VALIDATE_USER_PHONE + paramsSerializer(params))
    .get()
    .json<APIResult<boolean>>()
}

export function getIdentityPassword() {
  return http(API.IDENTITY_PASSWORD).get().json<PasswordPolicy>()
}
