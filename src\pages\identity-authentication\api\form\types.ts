export interface FileOptions {
  binaryData: string;
  fileAccessPath: string;
  fileRealName: string;
  fileSaveName: string;
  fileSize: string;
  fileSuffix: string;
}

export interface AnnexFileParams {
  ExtName: string;
  FileName: string;
  FilePath: string;
  ProcessDefId: string;
  UploadUserId: string;
  UploadUserName: string;
}

export interface AnnexFile {
  id: string;
  state: number;
  extName: string;
  stepId: string | null;
  stepName: string | null;
  sourceId: string | null;
  fileName: string;
  filePath: string;
  fieldCode: string | null;
  processDefId: string;
  processName: string | null;
  uploadUserId: string;
}
