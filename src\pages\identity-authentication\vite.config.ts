import { resolve } from 'path'
import Unocss from 'unocss/vite'
import ElementPlus from 'unplugin-element-plus/vite'
import Icons from 'unplugin-icons/vite'
import { defineConfig } from 'vite'
import Pages from 'vite-plugin-pages'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    Unocss(),
    Pages({
      dirs: resolve(__dirname, './pages'),
      exclude: ['**/components/*.vue'],
    }),
    Icons({
      autoInstall: true,
    }),
    ElementPlus({}),
  ],
  esbuild: {
    target: 'esnext',
    supported: {
      'top-level-await': true, //browsers can handle top-level-await features
    },
  },
})
