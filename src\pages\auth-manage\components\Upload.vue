<script lang="ts" setup>
import { useUser } from '@/oidc'
import { createRandomID, createUUID } from '~/utils'
import type { AnnexFileParams, FileOptions } from '~auth-manage/api/form'
import MaterialSymbolsAdd from '~icons/material-symbols/add'
import {
  ElNotification,
  type UploadFile,
  type UploadRequestOptions,
  type UploadUserFile,
} from 'element-plus'
import type { UploadAjaxError } from 'element-plus/es/components/upload/src/ajax'
import type { Awaitable, EpPropMergeType } from 'element-plus/es/utils'

type Files = Map<number, FileOptions>

const props = withDefaults(
  defineProps<{
    accept?: string
    listType?:
      | EpPropMergeType<StringConstructor, 'picture-card' | 'picture' | 'text', unknown>
      | undefined
    files?: string[]
    modelValue?: Array<FileOptions>
    type?: '' | 'annex'
    processId?: string
    limit?: number
  }>(),
  {
    modelValue: () => [],
    accept: '.jpg,.png,.jpeg',
    listType: 'picture-card',
  },
)
const emit = defineEmits<{
  (e: 'update:modelValue', value: Array<FileOptions>): void
}>()

const files = reactive<Files>(new Map())
const fileList = ref<UploadUserFile[]>([])
const vModel = useVModel(props, 'modelValue', emit)
const user = useUser()
const { form } = useApi()

function createFileFormData(file: File) {
  const formData = new FormData()
  formData.append('File', file)
  formData.append('IsReName', false as unknown as string)
  formData.append('IsLanguage', false as unknown as string)
  formData.append('UploadType', 2 as unknown as string)
  formData.append('DirName', createUUID())
  formData.append('FileName', file.name)
  return formData
}
function onSuccess() {
  ElNotification({
    type: 'success',
    message: '上传成功',
  })
}
function onError(error: Error) {
  ElNotification({
    type: 'error',
    message: String(error),
  })
}
function onRemove(uploadFile: UploadFile): Awaitable<boolean> {
  files.delete(uploadFile.uid)
  onUpdate()
  return true
}
function annexParams(data: FileOptions): AnnexFileParams {
  return {
    ExtName: data.fileRealName,
    FileName: data.fileRealName,
    FilePath: data.fileAccessPath,
    ProcessDefId: props.processId!,
    UploadUserId: user?.profile.UserAccount!,
    UploadUserName: user?.profile.UserName!,
  }
}
function onHttpRequest(options: UploadRequestOptions): Promise<unknown> {
  const file = options.file as UploadUserFile
  const formData = createFileFormData(file as File)
  return form
    .uploadFiles(formData)
    .then(async ({ code, data, message }) => {
      if (code === 1) {
        if (props.type === 'annex') {
          const { code: code1, data: data1 } = await form.uploadAnnexFiles(annexParams(data))
          if (code1 === 1) {
            files.set(file.uid!, data1[0] as any)
          }
        } else {
          files.set(file.uid!, data)
        }
      } else {
        options.onError(message as unknown as UploadAjaxError)
      }
    })
    .catch((res) => options.onError(res))
}

function onUpdate() {
  const values = ref<FileOptions[]>([])
  files.forEach((value) => values.value.push(value))
  vModel.value = values.value
}

watch(
  files,
  () => {
    onUpdate()
  },
  {
    deep: true,
  },
)

onMounted(() => {
  if (props.files) {
    fileList.value = props.files.map((item) => {
      const id = createRandomID(6)
      files.set(id, {
        binaryData: '',
        fileAccessPath: item,
        fileRealName: item.split('/')?.[item.split('/').length - 1],
        fileSaveName: item.split('/')?.[item.split('/').length - 1],
        fileSize: '',
        fileSuffix: item.split('.')?.[item.split('.').length - 1],
      })
      return {
        uid: id,
        name: item,
        url: useImageURL(item),
      }
    })
  }
})
function onExceed(files: File[]) {
  console.log('files', files)

  return ElNotification({
    type: 'error',
    title: '上传失败',
    message: `超出文件最大上传 ${props.limit} 限制`,
  })
}
</script>

<template>
  <el-upload
    v-model:file-list="fileList"
    multiple
    :list-type="listType"
    :limit="limit"
    :accept="accept"
    :on-success="onSuccess"
    :on-error="onError"
    :before-remove="onRemove"
    :http-request="onHttpRequest"
    :on-exceed="onExceed"
  >
    <slot v-if="listType === 'text'" name="default"></slot>
    <el-icon v-else>
      <MaterialSymbolsAdd />
    </el-icon>
  </el-upload>
</template>

<style lang="less" scoped></style>
