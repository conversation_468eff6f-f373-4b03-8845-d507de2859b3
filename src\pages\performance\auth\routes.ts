import type { RouteRecordRaw } from "vue-router";

export const oidcRoutes: Array<RouteRecordRaw> = [
  {
    path: "/oidc-callback",
    name: "oidc-callback",
    component: () => import("./components").then((res) => res.CallbackRepl),
  },
  {
    path: "/oidc-popup-callback",
    name: "oidc-popup-callback",
    component: () => import("./components").then((res) => res.PopupCallbackRepl),
  },
  {
    path: "/silent-renew-oidc",
    name: "silent-renew-oidc",
    component: () => import("./components").then((res) => res.SilentRenewCallbackRepl),
  },
];
