<script setup lang="ts">
import { useVant } from '@/vant'
import { showConfirmDialog } from 'vant'

const { VantConfigProvider, theme } = useVant({
  theme: 'light',
  tag: 'span',
  themeVars: {
    buttonDefaultBackground: '#34348f',
    buttonDefaultColor: '#ff8282',
  },
})
</script>

<template>
  <h2>Vant</h2>
  <h3>VantConfigProvider</h3>
  <VantConfigProvider>
    <VanButton> 按钮 </VanButton>
    <VanCalendar
      title="Calendar"
      :poppable="false"
      :show-confirm="false"
      :style="{ height: '500px' }"
    />
  </VantConfigProvider>

  <h3>SwitchTheme</h3>
  <VanButton @click="theme = 'light'"> To Light </VanButton>
  <VanButton @click="theme = 'dark'"> To Dark </VanButton>

  <h3>ShowDialog</h3>
  <VanButton
    @click="
      showConfirmDialog({
        title: '标题',
        message: '我是消息',
      })
    "
  >
    To ShowDialog
  </VanButton>

  <h2>Vant I18n</h2>

  <h3>CurrentLocale</h3>

  <h3>SwitchLocale</h3>
</template>

<style scoped></style>
