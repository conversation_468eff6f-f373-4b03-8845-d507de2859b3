<script setup lang="ts">
import { useAuth } from 'vue-oidc-provider'

const { isAuthenticated, isLoading, error, signinRedirect, user, removeUser } = useAuth()
</script>
<template>
  <div v-if="isLoading">Loading...</div>
  <div v-if="error">Oops... {{ error.message }}</div>
  <div v-if="isAuthenticated">
    hello {{ user?.profile.UserName }}
    <button @click="removeUser">退出</button>
  </div>
  <div v-else>
    <button @click="signinRedirect()">登陆</button>
  </div>
</template>

<style scoped></style>
