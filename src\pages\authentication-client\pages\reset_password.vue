<script lang="ts" setup>
import { isPasswordValid } from '../utils'
import { useResetRef } from '@unpages/utils'
import type { ResetPassword } from '~authentication-client/api/ids4/types'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useAuth } from 'vue-oidc-provider'

useTitle('统一身份认证-重制密码')

const { ids4 } = useApi()
const { user } = useAuth()
const formRef = ref<FormInstance>()
const { data: identityPassword } = await ids4.getIdentityPassword()
const [form, reset] = useResetRef<ResetPassword>({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})
const rules = reactive<FormRules<ResetPassword>>({
  oldPassword: [{ required: true, message: '旧密码不能为空', trigger: 'blur' }],
  newPassword: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (value === '') {
          callback(new Error('新密码不能为空'))
        } else if (value === form.value.oldPassword) {
          callback(new Error('新密码不能与旧密码相同'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
    {
      validator: (rule, value, callback) => {
        const { valid, message } = isPasswordValid(value, identityPassword.value!)
        if (!valid) {
          callback(new Error(message))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  confirmPassword: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次确认新密码'))
        } else if (value !== form.value.newPassword) {
          callback(new Error('两次密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
})

function submit() {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      const { data } = await ids4.resetPassword(form.value)
      if (data.value?.data === true) {
        ElMessage.success('修改密码成功')
        reset()
      } else {
        ElMessage.warning(data.value?.message)
      }
    } else {
      console.log('error submit!')
      return false
    }
  })
}
</script>

<template>
  <div class="h-60px px-40px bg-#222842 flex justify-between mb-40px">
    <div class="text-2rem color-blue font-500 flex items-center">
      <div i-material-symbols-key-rounded class="mr-10px"></div>
      <span class="text-1.1rem font-bold color-white">重制密码</span>
    </div>
    <div class="flex items-center">
      <el-avatar :src="user?.profile.Avatar" :size="30"></el-avatar>
      <div class="text-14px ml-10px color-white">
        {{ user?.profile.UserName }}
      </div>
    </div>
  </div>

  <div class="flex justify-center">
    <main class="w-800px">
      <el-form :model="form" label-position="top" label-width="auto" ref="formRef" :rules="rules">
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input
            v-model="form.oldPassword"
            type="password"
            autocomplete="off"
            placeholder="请输入旧密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="form.newPassword"
            type="password"
            autocomplete="off"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="重复密码" prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            autocomplete="off"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>

        <div class="text-center">
          <el-button type="primary" @click="submit">提 交</el-button>
        </div>
      </el-form>
    </main>
  </div>
</template>

<style scoped></style>
