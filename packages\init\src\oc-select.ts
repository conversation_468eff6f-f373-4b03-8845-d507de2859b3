import { isArray, merge } from 'lodash-es'
import { type Ref } from 'vue'

export function initOcSelect(host: string, callback?: (el: HTMLScriptElement) => void) {
  const ids4apiHost = host + '/ids4api'
  const scriptSrc = `${ids4apiHost}/orgselector/modal.js`
  useScriptTag(scriptSrc, (el) => {
    callback && callback(el)
  })
}

export type OCTypes =
  | 'U' //用户
  | 'J' //岗位
  | 'D' //部门
  | 'C' //公司
  | 'R' //角色
  | 'B' //相对岗位
  | 'ORG' //组织

interface CreateOCSelectorReturn {
  data: Ref<OcItem[] | undefined>
  instance: Modal
  show: () => void
  hide: () => void
  toggle: () => void
  hideModal: () => void
  open: () => Promise<CreateOCSelectorReturn>
}

interface Modal {
  new (options?: OcSelectorOptions): Modal
  _current: Modal
  $backdrop: Element
  open<S>(type?: string, options?: OcSelectorOptions<S>): void
  show(): void
  hide(): void
  toggle(): void
  hideModal(): void
}

export interface OCProps {
  multiple?: boolean
  // 调用选择的账号，用于保存常用联系人
  u?: string
  //范围限制
  scopes?: string
  // 已选择的
  selected?: { nodeId: string; tab: string; name: string }[]
  // 是否通过esc关闭
  closeOnPressEscape?: boolean
  // 是否通过点击modal关闭
  closeOnClickModal?: boolean
}

export interface OcItem<S = any> {
  id: string
  name: string
  type: OCTypes
  source: S
}

type APIResult<T = any> = {
  code: number | string
  count: number
  message: string | undefined
  data: T
}

export type OcSelectorResult<S> = APIResult<OcItem<S>[]>

export interface OcSelectorOptions<S = any> {
  iframeParams?: Omit<OCProps, 'selected'> & {
    t: string
    m: string
    selected: string
  }
  callback?: (result: OcSelectorResult<S>) => void
}

const propsDefault = {
  multiple: true,
  closeOnClickModal: true,
  closeOnPressEscape: true,
}

export function useOcSelector(
  type: OCTypes | OCTypes[] = ['U'],
  props: OCProps = {},
): CreateOCSelectorReturn & PromiseLike<CreateOCSelectorReturn> {
  const _props = merge(
    { ...propsDefault },
    {
      ...props,
      t: isArray(type) ? type.join(',') : type,
      m: props.multiple !== false ? 'm' : 's',
      selected: props.selected?.length ? JSON.stringify(props.selected) : '',
    },
  )
  const instance = new Modal({ iframeParams: _props })
  const data = ref<OcItem[]>()
  const show = () => instance.show()
  const hide = () => instance.hide()
  const toggle = () => instance.toggle()
  const hideModal = () => instance.hideModal()

  function open(): Promise<CreateOCSelectorReturn> {
    return new Promise((resolve, reject) => {
      const cleanup = ref<Function[]>([])
      instance.open(_props.t, {
        callback: (result) => {
          if (result.code === 0 && result.data.length) {
            data.value = result.data
            resolve(shell)
          } else {
            reject(result.message)
          }
          cleanup.value.forEach((fn) => fn && fn())
        },
      })
      cleanup.value = handleEvent()
    })
  }
  function handleEvent() {
    const cleanup: Function[] = []
    if (_props.closeOnPressEscape) {
      cleanup[0] = useEventListener(document, 'keydown', (event) => {
        if (event.key === 'Escape') {
          hide()
          cleanup?.forEach((fn) => fn && fn())
        }
      })
    }
    if (_props.closeOnClickModal) {
      cleanup[1] = useEventListener(instance.$backdrop, 'click', () => {
        hide()
        cleanup?.forEach((fn) => fn && fn())
      })
    }
    return cleanup
  }

  const shell: CreateOCSelectorReturn = {
    instance,
    data,
    open,
    show,
    hide,
    toggle,
    hideModal,
  }

  return {
    ...shell,
    then(onFulfilled, onRejected) {
      return new Promise(() => {
        open().then(onFulfilled, onRejected)
      })
    },
  }
}

declare global {
  const Modal: Modal
}
