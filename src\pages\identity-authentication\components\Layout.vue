<script lang="ts" setup></script>

<template>
  <el-container class="min-h-screen">
    <el-aside :width="menuCollapse ? '70px' : '220px'" class="h-screen">
      <slot name="aside" />
    </el-aside>
    <el-container>
      <el-header style="--el-header-padding: 0; --el-header-height: 52px">
        <slot name="header" />
      </el-header>
      <el-main class="bg-#f5f7fa">
        <slot />
      </el-main>
    </el-container>
  </el-container>
</template>

<style lang="less" scoped></style>
